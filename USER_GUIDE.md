# AutoSchemaKG Enhanced Pipeline User Guide

## Overview

This guide covers the enhanced AutoSchemaKG pipeline designed for processing 30+ PDF files with batch processing, retry mechanisms, and checkpoint resume capabilities. The pipeline has been specifically optimized for FOREX trading document processing.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [File Structure](#file-structure)
3. [Configuration Setup](#configuration-setup)
4. [Step-by-Step Processing Guide](#step-by-step-processing-guide)
5. [Command Reference](#command-reference)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Usage](#advanced-usage)

## Prerequisites

### System Requirements
- Python 3.8 or higher
- At least 8GB RAM (16GB recommended for large batches)
- 10GB+ free disk space
- Stable internet connection for API calls

### Required Dependencies
```bash
pip install -e .
# For NV-embed-v2 support (optional):
pip install -e .[nvembed]
```

### API Keys Required
- **Google Gemini API Key** (for LLM processing)
- **OpenAI API Key** (optional, for embedding models)

## File Structure

```
AutoSchemaKG/
├── config.ini                    # API configuration
├── pdf_process/                  # PDF processing tools
│   ├── pdf2md.py                # Enhanced batch PDF converter
│   └── data/                    # Input PDF directory
├── example_data/                # JSON input files for pipeline
├── import/                      # Output directory for all datasets
│   ├── dataset_name_1/         # Individual dataset outputs
│   ├── dataset_name_2/
│   └── ...
├── atlas_graphml_pipeline.py    # Main enhanced pipeline
├── run_concept_generation_robust.py  # Concept generation (standalone)
└── USER_GUIDE.md               # This file
```

## Configuration Setup

### 1. Create `config.ini`
```ini
[settings]
LLM_MODEL = gemini-2.0-flash-exp
EMBEDDING_MODEL = sentence-transformers/all-MiniLM-L6-v2

[api_keys]
GOOGLE_API_KEY = your_google_api_key_here
OPENAI_API_KEY = your_openai_api_key_here  # Optional
```

### 2. Directory Setup
```bash
# Create required directories
mkdir -p pdf_process/data
mkdir -p pdf_process/output
mkdir -p example_data
mkdir -p import
```

## Step-by-Step Processing Guide

### Scenario 1: Processing 30+ PDFs (Recommended)

#### Step 1: Prepare Your PDFs
```bash
# Place all PDF files in the pdf_process/data directory
cp /path/to/your/pdfs/*.pdf pdf_process/data/
```

#### Step 2: Convert PDFs to Markdown (Batch Mode)
```bash
cd pdf_process
python pdf2md.py
```

**What this does:**
- Processes all PDFs in `data/` directory
- Converts to markdown with checkpoint resume
- Outputs to `output/` directory
- Creates `pdf_conversion_checkpoint.json` for resume capability
- Retry mechanism: 3 attempts per PDF with exponential backoff

**Expected output:**
```
🚀 Enhanced PDF Batch Processor
==================================================
📊 Found 30 PDF files to process
📋 Already processed: 0
❌ Previous failures: 0

[1/30] Processing document1.pdf...
🔧 Initializing PDF converter...
✅ PDF converter initialized
📄 Converting document1.pdf (attempt 1/3)...
✅ Converted document1.pdf → document1.md
⏱️  Progress: 1/30 (3.3%) - ETA: 45.2min
```

#### Step 3: Convert Markdown to JSON
```bash
# From project root
python -m atlas_rag.kg_construction.utils.md_processing.markdown_to_json \
    --input pdf_process/output \
    --output example_data
```

#### Step 4: Run Enhanced Pipeline (Batch Mode)
```bash
python atlas_graphml_pipeline.py \
    --batch-mode \
    --dataset-name forex_trading_docs \
    --tier1
```

**Parameter Explanation:**
- `--batch-mode`: Process all JSON files in `example_data/` as separate datasets
- `--dataset-name forex_trading_docs`: Base name for datasets (each PDF becomes `forex_trading_docs_document1`, etc.)
- `--tier1`: Use token-optimized configuration for better API stability

### Scenario 2: Processing Single Dataset

#### For Single Document Processing:
```bash
# Place single JSON file in example_data/
cp my_document.json example_data/

python atlas_graphml_pipeline.py \
    --dataset-name my_forex_analysis \
    --filename-pattern my_document.json
```

## Command Reference

### PDF Processing (`pdf_process/pdf2md.py`)

**Basic Usage:**
```bash
cd pdf_process
python pdf2md.py
```

**Key Features:**
- **Input**: All `.pdf` files in `data/` directory
- **Output**: Markdown files in `output/` directory
- **Checkpoint**: `pdf_conversion_checkpoint.json`
- **Resume**: Run the same command to resume from failures

### Enhanced Pipeline (`atlas_graphml_pipeline.py`)

**Full Command Syntax:**
```bash
python atlas_graphml_pipeline.py [OPTIONS]
```

#### Required Options:
- `--dataset-name NAME`: Base name for output datasets

#### Processing Mode Options:
- `--batch-mode`: Process all JSON files as separate datasets
- `--filename-pattern PATTERN`: Process specific files (single mode only)

#### Performance Options:
- `--tier1`: Use token-optimized configuration (recommended)
- `--no-retry`: Disable retry mechanisms (faster but less robust)
- `--openai-wrapper`: Use OpenAI wrapper (not recommended)

#### Example Commands:

**Process 30 PDFs with optimal settings:**
```bash
python atlas_graphml_pipeline.py \
    --batch-mode \
    --dataset-name forex_batch_2024 \
    --tier1
```

**Process single document with retry disabled:**
```bash
python atlas_graphml_pipeline.py \
    --dataset-name single_analysis \
    --filename-pattern trading_guide.json \
    --no-retry
```

**Resume failed batch process:**
```bash
# Same command - pipeline will resume from checkpoint
python atlas_graphml_pipeline.py \
    --batch-mode \
    --dataset-name forex_batch_2024 \
    --tier1
```

## Understanding Dataset Names and Output Structure

### Dataset Naming Convention

When using `--dataset-name forex_trading_docs` with `--batch-mode`:

**Input files in `example_data/`:**
```
360t_guide.json
mifid_regulation.json
trading_manual.json
```

**Generated datasets:**
```
import/forex_trading_docs_360t_guide/
import/forex_trading_docs_mifid_regulation/
import/forex_trading_docs_trading_manual/
```

### Output Directory Structure

Each dataset directory contains:
```
import/forex_trading_docs_360t_guide/
├── kg_extraction/              # Raw extraction results
│   └── extraction_output.json
├── kg_csv/                     # CSV format data
│   ├── nodes.csv
│   └── edges.csv
├── concepts/                   # Generated concepts
│   └── concept_shard_0.csv
├── kg_graphml/                 # GraphML output
│   └── knowledge_graph.graphml
├── pipeline_checkpoint.json    # Resume checkpoint
└── pipeline_summary.txt        # Processing summary
```

## Current Working Example

Based on your current setup with the `360t_guide_direct_api_v2` dataset:

### Existing Dataset:
```bash
# Your current working dataset
import/360t_guide_direct_api_v2/
├── concepts/concept_shard_0.csv  # 263 FOREX concepts generated
├── kg_extraction/
├── kg_csv/
└── ...
```

### Process Additional Documents:
```bash
# Add new PDFs to your batch
cp new_trading_docs/*.pdf pdf_process/data/

# Process with similar naming
python atlas_graphml_pipeline.py \
    --batch-mode \
    --dataset-name 360t_extended_v3 \
    --tier1
```

This would create:
```
import/360t_extended_v3_document1/
import/360t_extended_v3_document2/
import/360t_extended_v3_document3/
```

## Troubleshooting

### Common Issues and Solutions

#### 1. API Rate Limits
**Symptoms:** `429 Too Many Requests` or `max remote calls: 10` errors

**Solution:**
```bash
# Use tier1 mode with built-in rate limiting
python atlas_graphml_pipeline.py --tier1 --batch-mode --dataset-name my_data
```

#### 2. Pipeline Failures
**Symptoms:** Pipeline stops mid-process

**Solution:**
```bash
# Resume from checkpoint - run the exact same command
python atlas_graphml_pipeline.py --batch-mode --dataset-name my_data --tier1
```

#### 3. PDF Conversion Failures
**Symptoms:** Some PDFs fail to convert

**Solution:**
```bash
# Check failed files in checkpoint
cat pdf_process/pdf_conversion_checkpoint.json

# Re-run to retry failed files
cd pdf_process && python pdf2md.py
```

#### 4. Memory Issues
**Symptoms:** Out of memory errors

**Solution:**
```bash
# Use tier1 mode with smaller batches
python atlas_graphml_pipeline.py --tier1 --dataset-name my_data
```

#### 5. Empty Concept Files
**Symptoms:** Concept CSV files are empty

**Solution:**
```bash
# Run standalone robust concept generation
python run_concept_generation_robust.py

# Or use integrated version (already included in enhanced pipeline)
python atlas_graphml_pipeline.py --dataset-name my_data
```

### Checkpoint Files

The pipeline creates several checkpoint files for resume capability:

1. **PDF Processing**: `pdf_process/pdf_conversion_checkpoint.json`
2. **Pipeline Processing**: `import/{dataset_name}/pipeline_checkpoint.json`
3. **Concept Generation**: `import/{dataset_name}/concept_checkpoint.json`

### Log Analysis

Check pipeline progress and errors:
```bash
# View pipeline summary for a dataset
cat import/my_dataset/pipeline_summary.txt

# Check checkpoint status
cat import/my_dataset/pipeline_checkpoint.json
```

## Advanced Usage

### Custom Batch Processing
```bash
# Process specific file types only
find example_data -name "*trading*" -type f | while read file; do
    python atlas_graphml_pipeline.py \
        --dataset-name $(basename "$file" .json) \
        --filename-pattern $(basename "$file")
done
```

### Performance Monitoring
```bash
# Monitor processing with timestamps
python atlas_graphml_pipeline.py --batch-mode --dataset-name monitor_test 2>&1 | \
    while IFS= read -r line; do
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] $line"
    done
```

### Parallel Processing
```bash
# Split large batches for parallel processing
# Process first half
python atlas_graphml_pipeline.py --batch-mode --dataset-name batch_part1

# Process second half (in another terminal)
python atlas_graphml_pipeline.py --batch-mode --dataset-name batch_part2
```

## Production Recommendations

### For 30+ PDF Processing:
1. **Always use `--tier1`** for better API stability
2. **Use `--batch-mode`** for efficient processing
3. **Monitor disk space** - each dataset needs ~100-500MB
4. **Run during off-peak hours** to avoid API rate limits
5. **Keep checkpoint files** until processing is completely done

### For Critical Processing:
```bash
# Recommended production command
python atlas_graphml_pipeline.py \
    --batch-mode \
    --dataset-name production_forex_$(date +%Y%m%d) \
    --tier1 \
    2>&1 | tee processing_log_$(date +%Y%m%d_%H%M).txt
```

This creates a timestamped log file and uses optimal settings for reliability.

## Next Steps After Processing

Once your pipeline completes successfully:

1. **Review Generated Files**: Check `import/{dataset_name}/pipeline_summary.txt`
2. **Verify Concepts**: Examine `import/{dataset_name}/concepts/concept_shard_0.csv`
3. **Neo4j Import**: Use generated GraphML files for knowledge graph databases
4. **RAG Integration**: Use the structured data for question-answering systems

The enhanced pipeline ensures your FOREX trading documents are processed reliably with comprehensive retry and resume capabilities.