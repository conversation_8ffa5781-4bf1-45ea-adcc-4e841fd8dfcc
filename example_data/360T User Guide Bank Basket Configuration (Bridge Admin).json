[{"id": "1", "text": "## BAN<PERSON> BASKET CONFIGURATION (BRIDGE ADMINISTRATION)\n\nImage /page/0/Picture/1 description: {\n \"image\\_description\": \"The image shows a logo for \\\"360T\\\", which is a green rectangle with rounded corners. The text \\\"360\\\" is in white with a green outline, and the \\\"T\\\" is also in white with a green outline. There are two arrows pointing towards each other in the middle of the \\\"0\\\" in \\\"360\\\".\"\n}\n\n# TEX MULTIDEALER TRADING SYSTEM\n\nUSER GUIDE 360T BRIDGE ADMINISTRATION:\n\nENHANCED BANK BASKET CONFIGURATION\n\n© 360 TREASURY SYSTEMS AG, 2019 THIS FILE CONTAINS PROPRIETARY AND CONF<PERSON>ENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG\n\n## CONTENTS\n\n| 1 |       | INTRODUCTION                          | 4  |\n|---|-------|---------------------------------------|----|\n| 2 |       | GETTING STARTED                       | 4  |\n| 3 |       | CONFIGURATION OF BANK BASKETS         | 7  |\n|   | 3.1   | SETTING UP CONFIGURATION GROUPS       | 8  |\n|   | 3.1.1 | Defining Currency Groups              | 9  |\n|   | 3.1.2 | Defining Currency Couple Groups       | 11 |\n|   | 3.1.3 | Defining FX Time Period Groups        | 13 |\n|   | 3.1.4 | Defining MM Time Period Groups        | 14 |\n|   | 3.1.5 | Defining Product Groups               | 15 |\n|   | 3.2   | PROVIDER GROUPS AND BLOCKED PROVIDERS | 16 |\n|   | 3.3   | BANK BASKET RULES                     | 18 |\n|   | 3.3.1 | Defining Bank Basket Rules            | 18 |\n|   | 3.3.2 | Order of the Rules                    | 19 |\n|   | 3.4   | NOTE ON SUPERSONIC                    | 19 |\n| 4 |       | BANK BASKETS EVALUATOR TOOL           | 19 |\n| 5 |       | CONTACT 360T                          | 22 |\n\n## TABLE OF FIGURES\n\n| Figure 1 Header Bar                                             | 4  |\n|-----------------------------------------------------------------|----|\n| Figure 2 Bridge Administration: Homepage                        | 5  |\n| Figure 3 Bank Basket: Start page                                | 5  |\n| Figure 4 Bank Basket: Configuration for a selected legal entity | 6  |\n| Figure 5 Bank Basket: Configuration: Live Audit Log             | 7  |\n| Figure 6 Bank Basket: Configuration Data Tabs                   | 8  |\n| Figure 7 Bank Basket: Currency Groups                           | 9  |\n| Figure 8 Bank Basket: RFS MM Bank Baskets                       | 10 |\n| Figure 9 Bank Basket: Currency Groups Default Group             | 10 |\n| Figure 10 Bank Basket: Currency Groups Create Group             | 11 |\n| Figure 11 Bank Basket: Configured Currency Group                | 11 |\n| Figure 12 Bank Basket: Currency Couple Groups                   | 12 |\n| Figure 13 Bank Basket: Add Currency Couple                      | 13 |\n| Figure 14 Bank Basket: Add FX Time Period                       | 14 |\n| Figure 15 Bank Basket: Add MM Time Period                       | 15 |\n| Figure 16 Bank Basket: Product Groups                           | 16 |\n| Figure 17 Bank Basket: Product Groups Create Group              | 16 |\n| Figure 18 Bank Basket: Blocked RFS Providers                    | 17 |\n| Figure 19 Bank Basket: RFS Provider Groups                      | 17 |\n\n| Figure 20 Bank Basket: Add Rule  18                     |  |\n|---------------------------------------------------------|--|\n| Figure 21 Use Custom selection for Currency couples  19 |  |\n| Figure 22 Bank Basket: Order of Rules  19               |  |\n| Figure 23 Evaluator Tool Quick Link  20                 |  |\n| Figure 24 Evaluator Tool icon  20                       |  |\n| Figure 24 Evaluator Tool  21                            |  |\n| Figure 26 Evaluator Tool: Highlighted Rule  21          |  |\n\n## 1 INTRODUCTION\n\nThis user manual describes the Bank Basket feature of the 360T Bridge Administration tool. The Bank Basket feature has been enhanced to provide improved rule management capabilities, including the introduction of configuration groups based on currency, currency couple, time period and product(s); configuration of separate baskets by request type (RFS, Order, SEP); as well as the ability to apply and remove temporary blocks without affecting the configured rules or counterpart relationship(s).\n\n#### Please note:\n\nThe 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.\n\nOnly users with corresponding user rights are able to administer the company's Bank Baskets. <NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.\n\n## 2 GETTING STARTED\n\nThe Bank Basket configuration is found within the Bridge Administration tool. Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application.\n\n|                                                                             |                                                                                     |                                                                            | $\\vee$ Preferences<br>V Administration                                 | $\\times$ Help<br>$\\Box$<br>A <sub>A</sub><br>$\\mathbb{X}$ |\n|-----------------------------------------------------------------------------|-------------------------------------------------------------------------------------|----------------------------------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------|\n| > Bridge Administration                                                     |                                                                                     |                                                                            |                                                                        | X                                                         |\n| <b>SCIENCES</b>                                                             |                                                                                     |                                                                            | <b>SCIENS</b>                                                          |                                                           |\n| <b>UNIT SIDEN</b><br>$^{117}890$<br><sup>⊞7</sup> 92α<br>Spot // 21.11.2017 | <b><i>STATISTICS</i></b><br><b>POST MARK</b><br>1327<br>##336<br>Spot // 21 11.2017 | <b>STATISTICS</b><br><b>START CORPORA</b><br>084<br>0.55<br>5001//21112017 | <b>Life of Life and</b><br>889184<br>0.994<br>93.<br>Spot // 2111 2017 |                                                           |\n| 11703<br><b>ILFOG.</b>                                                      | 132.2 d                                                                             | 08900 mm<br><b>TIDO AT TELL</b>                                            | 0.99 ft J Lune<br>0994 T                                               |                                                           |\n\nFigure 1 Header Bar\n\nThe Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.\n\nA quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\n\nImage /page/4/Picture/0 description: {\n \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user guide. The screen displays the 'Administration Start' page with options for 'Regulatory Data', 'Bank Baskets', 'Change Request', 'Wizards', and 'Evaluator Tools'. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with preferences and help options. The bottom of the screen displays system information and connection status.\"\n}\n\nFigure 2 Bridge Administration: Homepage\n\nThe \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree. Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.\n\n|  |  |                      |                      |                              |   |  |  | Preferences | Administration | Help | A A |\n|--|--|----------------------|----------------------|------------------------------|---|--|--|-------------|----------------|------|-----|\n|  |  | <b>RFS Requester</b> | <b>DEAL TRACKING</b> | <b>Bridge Administration</b> | + |  |  |             |                |      |     |\n\n Q\n \n 360T.ALIAS\n \n 360T.ALIAS.Company 1\n \n 360T.ALIAS.Company 2\n \n 360T.ALIAS.Company 3\n \n 360T.ALIAS.Company 4\n \n 360T.ALIAS.Company 5\n \n 360T.ALIAS.Company 6\n \n >>\n \n No Individuals/Institutions are selected\n\n| 360TAS Treasurer 1, 360T ALIAS // INT | Mi, 25. Apr 2018, 14:42:22 GMT // Connected |\n|---------------------------------------|---------------------------------------------|\n|---------------------------------------|---------------------------------------------|\n\nFigure 3 Bank Basket: Start page\n\nThe selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity. It is possible to open multiple forms/sheets at a time. The selected item will be highlighted as an active task inside the taskbar.\n\nImage /page/5/Picture/0 description: {\n \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The interface includes a navigation menu on the left, a main content area in the center, and a header with user preferences and administration options. The navigation menu lists several 360T.ALIAS companies. The main content area displays currency group settings, with options to create and manage currency groups. The header includes tabs for different configuration groups, such as RFS Bank Baskets, Orders Bank Baskets, and SEP Bank Baskets.\"\n}\n\nFigure 4 Bank Basket: Configuration for a selected legal entity\n\nA set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:\n\n- Search : A search field will open and the user can type in an alphanumeric value in order to find the desired institution.\n- Scroll from source : This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel. Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.\n- Show individuals view toggle : This icon is deactivated when using the Bank Basket configuration. For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.\n- Show institutions view toggle : This icon is deactivated when using the Bank Basket configuration. For other configuration tools the toggle option allows the user to display only institutions in the navigation panel.\n\nThe navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.\n\nEach entity tab has a Live Audit Log which tracks all unsaved changes. Individual unsaved changes can be reverted by clicking on the arrow icon. Clicking on the \"Discard all changes\" button will revert all unsaved changes.\n\nImage /page/6/Picture/0 description: {\n \"image\\_description\": \"The image contains the text 'User Guide 360T Bank Baskets Configuration'. The text appears to be the title or heading of a document or guide.\"\n}\n\n|                             | 9 ※ 上                                    |                                                         |                                   |                                          |   |            | Live Audit Log   | 目<br>$\\Omega$ |\n|-----------------------------|------------------------------------------|---------------------------------------------------------|-----------------------------------|------------------------------------------|---|------------|------------------|---------------|\n| $\\mathcal{L}_{\\mathcal{F}}$ | へ 盒 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | <b>Currency Groups</b><br><b>Currency Couple Groups</b> | FX Time Period Groups             | MM Time Period ( )                       |   | Target     | Event Name       |               |\n|                             | 直 360T.ALIAS.Company 2                   |                                                         |                                   |                                          |   | 360T.ALIAS | Currency Added   | $\\sqrt{2}$    |\n| ₿                           | 盒 360T.ALIAS.Company 3                   | <b>Currency Group</b>                                   |                                   |                                          |   | 360T.ALIAS | Currency Added   | $\\mathcal{L}$ |\n|                             | 盒 360T.ALIAS.Company 4                   | Default Group                                           |                                   | a <sub>1</sub>                           |   | 360T ALIAS | Currency Added   | $\\sqrt{2}$    |\n| <b>tip</b>                  | 宜 360T.ALIAS.Company 5                   |                                                         |                                   | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! |   | 360T.ALIAS | Currency Added   | $\\mathcal{L}$ |\n|                             | 盒 360T.ALIAS.Company 6                   |                                                         |                                   |                                          |   | 360T.ALIAS | Currency Added   | $\\mathcal{L}$ |\n| 同                           |                                          |                                                         |                                   |                                          |   | 360T.ALIAS | Currency Added   | $\\sqrt{2}$    |\n|                             |                                          |                                                         |                                   |                                          | √ | 360T.ALIAS | Currency Removed |               |\n| $\\vec{\\Sigma}$              |                                          |                                                         |                                   |                                          |   |            |                  |               |\n| ಥೆ                          |                                          |                                                         |                                   |                                          |   |            |                  |               |\n|                             |                                          |                                                         |                                   |                                          |   |            |                  |               |\n|                             |                                          |                                                         |                                   | <b>DX</b><br>$ 1^{2}$                    |   |            |                  |               |\n|                             |                                          | Available Currencies                                    | Select Member for \"Default Group\" | <b>Selected Currencies</b>               |   |            |                  |               |\n|                             |                                          |                                                         | AUD                               |                                          |   |            |                  |               |\n|                             |                                          | SEK                                                     | $\\geq$<br>CAD                     |                                          |   |            |                  |               |\n|                             |                                          | SGD<br>ZAR                                              | $\\overline{\\epsilon}$<br>CHF      |                                          |   |            |                  |               |\n|                             |                                          | AED                                                     | EUR                               |                                          |   |            |                  |               |\n|                             |                                          | AFN                                                     | GBP                               |                                          |   |            |                  |               |\n|                             |                                          | ALL                                                     | $\\gg$<br><b>USD</b>               |                                          |   |            |                  |               |\n|                             |                                          | AMD                                                     | $\\ll$                             |                                          |   |            |                  |               |\n|                             |                                          | ANG                                                     |                                   |                                          |   |            |                  |               |\n|                             |                                          |                                                         |                                   |                                          |   |            |                  |               |\n|                             |                                          |                                                         |                                   |                                          |   |            |                  |               |\n|                             |                                          |                                                         |                                   |                                          |   |            |                  |               |\n\nFigure 5 Bank Basket: Configuration: Live Audit Log\n\n## 3 CONFIGURATION OF BANK BASKETS\n\nSelecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs:\n\n- Configuration Groups: Facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS; Order; SEP). Allow users to create a group one single time and reuse it across various rules.\n- RFS Bank Baskets: Provider Groups and temporarily Blocked Providers may be configured specifically for RFS requests. Bank Basket rules for four separate request types are configured on this tab:\n  - o RFS FX Bank Baskets\n  - o RFS MM Bank Baskets\n  - o RFS Commodity Bank Baskets\n  - o RFS Cross Currency Netting Bank Baskets\n- Order Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Orders. Bank Basket rules for Forward or Spot orders are configured on this tab.\n- SEP Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP). Bank Basket rules for SEP streaming spot executions are configured on this tab.\n\nImage /page/7/Picture/0 description: {\n \"image\\_description\": \"The image shows the logo of DEUTSCHE GROUP. The logo consists of two parts: a green graphic element on the left and the text \\\"DEUTSCHE GROUP\\\" on the right. The graphic element is a stylized representation of the letters \\\"360\\\" and a \\\"T\\\" inside a rounded rectangle. The text \\\"DEUTSCHE GROUP\\\" is in a sans-serif font, with \\\"DEUTSCHE\\\" stacked above \\\"GROUP\\\".\"\n}\n\n| RFS Requester | DEAL TRACKING | Bridge Administration | + | Preferences | Administration | Help | AA | X |\n|---------------|---------------|-----------------------|---|-------------|----------------|------|----|---|\n|---------------|---------------|-----------------------|---|-------------|----------------|------|----|---|\n\n|  |  | <input type=\"text\"/> |\n|--|--|----------------------|\n|--|--|----------------------|\n\n|  | 360T.ALIAS           |\n|--|----------------------|\n|  | 360T.ALIAS.Company 1 |\n|  | 360T.ALIAS Company 2 |\n|  | 360T.ALIAS.Company 3 |\n|  | 360T.ALIAS.Company 4 |\n|  | 360T.ALIAS.Company 5 |\n|  | 360T.ALIAS.Company 6 |\n\n| Configuration Groups | RFS Bank Baskets       | Orders Bank Baskets   | SEP Bank Baskets      |                |\n|----------------------|------------------------|-----------------------|-----------------------|----------------|\n| Currency Groups      | Currency Couple Groups | FX Time Period Groups | MM Time Period Groups | Product Groups |\n\n| Currency Group |\n|----------------|\n| Default Group  |\n\n|  |  | Create Group |\n|--|--|--------------|\n|--|--|--------------|\n\n| Create Change Request | Discard All Changes | Save |\n|-----------------------|---------------------|------|\n|-----------------------|---------------------|------|\n\n| 360T.ALIAS Company 1 X |\n|------------------------|\n|------------------------|\n\n| 360TAS Treasurer 1, 360T ALIAS // INT | Tue, 19. Jun 2018, 15:59:16 GMT // Connected [FFM] |\n|---------------------------------------|----------------------------------------------------|\n|---------------------------------------|----------------------------------------------------|\n\nFigure 6 Bank Basket: Configuration Data Tabs\n\nBank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another i.e., an RFS configuration will have no impact on SEP trading, and vice versa.\n\n### 3.1 Setting up Configuration Groups\n\nConfiguration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.\n\nGroups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP. The groups themselves can be edited (values added or removed) without changing a set of rules based on those groups.\n\nThe available parameters are:\n\n- o Currency Groups\n- o Currency Couple Groups\n- o FX Time Period Groups\n- o MM Time Period Groups\n\nA set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.\n\n- Create Group : To add a new group.\n- Rename Group : To change the name of a group. Cannot be used on the Default Group.\n\n- Remove Group : To delete an individual group. Cannot be used on the Default Group. If the removed group is used in any configured rules this group is replaced by the Default Group.\n- Save : Please remember to save changes to your configurations.\n\nConfiguration Groups are particularly useful when configuring complex bank basket rules.\n\n- Note: It is not required to configure groups based on the above parameters. Users may still set individual custom rules without utilizing the Configuration Groups. Individual custom rules may be preferable for some users with less complex bank basket setups.\n- Note: Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group. The Default Group will include all existing values.\n\nAll Default Groups can be modified (values may be removed). In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group. For example, if a new currency is added to the 360T platform the Default Currency Group will not include the new currency. The new currency must be selected by the user.\n\n#### 3.1.1 Defining Currency Groups\n\nCurrency Groups are intended to allow the classification of single currencies into customized groups. This allows setting one single rule for each group of currencies rather than many rules for individual currencies.\n\nCurrencies can be added or removed from the group without editing the rules themselves.\n\nImage /page/8/Picture/11 description: {\n \"image\\_description\": \"The image shows a user interface for managing currency groups in a financial application. The interface includes a list of available currencies and a list of selected currencies, allowing users to add or remove currencies from a group. The user is currently creating a currency group named G10.\"\n}\n\nFigure 7 Bank Basket: Currency Groups\n\nOnce created, a currency group can be used to simplify rule creation for (1) interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and Tri Party Repo found in the RFS MM Bank Baskets area and (2) Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.\n\nImage /page/9/Picture/0 description: {\n \"image\\_description\": \"The image shows a green and white logo with the number 360 and a symbol that looks like a T.\"\n}\n\nUser Guide 360T Bank Baskets Configuration\n\n| 侖                                | Q ※ 上 二<br>$\\langle$                                       |              | <b>Configuration Groups</b> | <b>RFS Bank Baskets</b>      | Orders Bank Baskets                     | SEP Bank Baskets Copy To | $ \\mathcal{E}_\\lambda $    |              |                                         |              | の位置      |\n|----------------------------------|------------------------------------------------------------|--------------|-----------------------------|------------------------------|-----------------------------------------|--------------------------|----------------------------|--------------|-----------------------------------------|--------------|----------|\n| $\\mathcal{G}$                    | $\\wedge$ $\\hat{\\Xi}$ 360T.ALIAS<br>盒 360T.ALIAS.Company 1  |              | RFS Provider Groups         | <b>Blocked RFS Providers</b> | RFS FX Bank Baskets RFS MM Bank Baskets | --                       | RFS Commodity Bank Baskets |              | RFS Cross Currency Netting Bank Baskets |              |          |\n| -<br>$\\Box$                      | 盒 360T.ALIAS.Company 2                                     |              | POS Product Type            |                              | Currency                                |                          | <b>Time Period</b>         |              | <b>Bank Basket</b>                      |              |          |\n| 凾                                | 盒 360T.ALIAS.Company 3<br><sup> 360T ALIAS Company 4</sup> | $\\mathbf{1}$ | Any                         | $\\vee$                       | Any                                     | $\\widehat{\\phantom{a}}$  | Any                        | $\\checkmark$ | Default Group                           | $\\checkmark$ | 図自       |\n|                                  | 盒 360T ALIAS Company 5<br>章 360T ALIAS Company 6           |              |                             |                              | Any<br>Default Group                    |                          |                            |              |                                         |              | Add Rule |\n| $\\quad \\  \\  \\, \\textcircled{F}$ |                                                            |              |                             |                              | 610.                                    |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              | Custom                                  |                          |                            |              |                                         |              |          |\n| ಂದ್ರಿ                            |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              |                                         |              |          |\n|                                  |                                                            |              |                             |                              |                                         |                          |                            |              | Discard All Changes                     | <b>Save</b>  |          |\n\nFigure 8 Bank Basket: RFS MM Bank Baskets\n\nA default group exists which includes all currencies. This group cannot be removed or renamed. However, the currencies in the group can be altered as described below.\n\n|  |                                                                                                                                              | Preferences Administration Help AA - X                                                                                                                           |      |\n|--|----------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|------|\n|  | RFS REQUESTER DEAL TRACKING                                                                                                                  | + BRIDGE ADMINISTRATION                                                                                                                                          |      |\n|  | Q                                                                                                                                            | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To...                                                                            |      |\n|  | 360T ALIAS                                                                                                                                   | Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product Groups                                                                |      |\n|  | 360T.ALIAS Company 1<br>360T.ALIAS.Company 2<br>360T.ALIAS.Company 3<br>360T.ALIAS.Company 4<br>360T.ALIAS.Company 5<br>360T.ALIAS.Company 6 | Currency Group <div>Default Group</div> <div>G10</div> <div>Create Group</div>                                                                                   |      |\n|  |                                                                                                                                              |                                                                                                                                                                  |      |\n|  |                                                                                                                                              | Select Member for \"Default Group\"                                                                                                                                | - X  |\n|  |                                                                                                                                              | Available Currencies Selected Currencies <div>AUD</div> <div>CAD</div> <div>CHF</div> <div>EUR</div> <div>GBP</div> <div>HKD</div> <div>JPY</div> <div>NOK</div> |      |\n|  |                                                                                                                                              | Discard All Changes Create Change Request 360T.ALIAS X                                                                                                           | Save |\n|  | 360TAS.Treasurer1, 360T ALIAS // INT                                                                                                         | Fri, 06. Jul 2018, 07:39:04 GMT // Connected [FFM]                                                                                                               |      |\n\nFigure 9 Bank Basket: Currency Groups Default Group\n\nCurrencies may be added or removed from the default group. A currency is highlighted with a single-click. This activates the single arrow. Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.\n\nAll currencies can be moved in either direction by using the double arrows.\n\nTo add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.\n\nPlease, provide a name for a new group\n\n|  | G10 |\n|--|-----|\n|--|-----|\n\n| Cancel | Create Group |\n|--------|--------------|\n|--------|--------------|\n\nFigure 10 Bank Basket: Currency Groups Create Group\n\nTo view the currencies configured for the group, click on the Currency Group name.\n\n| Preferences | Administration | Help | AA |\n|-------------|----------------|------|----|\n|-------------|----------------|------|----|\n\nRFS REQUESTER\nDEAL TRACKING\nBRIDGE ADMINISTRATIONConfiguration Groups\nRFS Bank Baskets\nOrders Bank Baskets\nSEP Bank Baskets\nCopy To...Currency Groups\nCurrency Couple Groups\nFX Time Period Groups\nMM Time Period Groups\nProduct GroupsCurrency Group\nDefault Group\nG10\nCreate GroupSelect Member for \"G10\"Available Currencies\nHKD\nPLN\nSGD\nZAR\nAED\nAFN\nALLSelected Currencies\nAUD\nCAD\nCHF\nEUR\nGBP\nJPY\nNOK\nNZDCreate Change Request\nDiscard All Changes\nSaveFigure 11 Bank Basket: Configured Currency Group\n\nNote: The same currency can be added to many different groups. The system does not restrict the creation of groups with overlapping sets of currencies.\n\n#### 3.1.2 Defining Currency Couple Groups\n\nCurrency Couple Groups allow the creation of \"buckets\" of currency pairs.\n\nOnce created, a Currency Couple Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS,\n\nOptions and Block Trades etc; (2) Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area; and also (3) Order Spot and Forwards found in the Orders Bank Basket area.\n\n|                                                                |                                                              |                                                 |                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\Diamond$ AA $\\Box$ $\\Box$ X |\n|----------------------------------------------------------------|--------------------------------------------------------------|-------------------------------------------------|----------------------------|-------------------------------------------------------------------------------------------|\n| RFS REQUESTER                                                  | <b>DEAL TRACKING</b><br><b>BRIDGE ADMINISTRATION</b>         | $+$                                             |                            |                                                                                           |\n|                                                                |                                                              |                                                 |                            |                                                                                           |\n| Q ※   上   三<br>侖                                               | <b>Configuration Groups</b><br>RFS Bank Baskets<br>$\\langle$ | Orders Bank Baskets SEP Bank Baskets<br>Copy To | $\\delta$                   | $\\mathcal{A} \\cap \\mathcal{A} \\equiv \\mathcal{A}$                                         |\n| △ 盒 360T.ALIAS                                                 | Currency Groups<br><b>Currency Couple Groups</b>             | FX Time Period Groups<br>MM Time Period Groups  | Product Groups             |                                                                                           |\n| €<br><sup> 360T.ALIAS.Company 1</sup>                          |                                                              |                                                 |                            |                                                                                           |\n| 查 360T.ALIAS.Company 2                                         |                                                              | <b>Currency Couple Group</b>                    |                            |                                                                                           |\n| ₿<br>盒 360T.ALIAS.Company 3                                    |                                                              | Default Group                                   |                            | al前                                                                                       |\n| 盒 360T ALIAS Company 4<br>$\\sqrt{2}$<br>盒 360T.ALIAS.Company 5 | <b>USD v G10</b>                                             |                                                 |                            | al fi                                                                                     |\n| 盒 360T.ALIAS.Company 6                                         |                                                              |                                                 |                            | Create Group                                                                              |\n| ₹                                                              |                                                              |                                                 |                            |                                                                                           |\n| $\\overline{\\mathcal{L}}$                                       |                                                              |                                                 |                            |                                                                                           |\n|                                                                |                                                              |                                                 |                            |                                                                                           |\n| 呢                                                              |                                                              |                                                 |                            |                                                                                           |\n|                                                                |                                                              |                                                 |                            | <b>DX</b><br>$ +$                                                                         |\n|                                                                |                                                              | Select Member for \"USD v G10\"                   |                            |                                                                                           |\n|                                                                |                                                              | <b>Base Currency</b><br><b>Quote Currency</b>   |                            |                                                                                           |\n|                                                                | <b>USD</b>                                                   | <b>EUR</b>                                      |                            | <b>シ</b> 童                                                                                |\n|                                                                | USD                                                          | JPY                                             |                            | ジョ                                                                                        |\n|                                                                | USD                                                          | GBP                                             |                            | ショ                                                                                        |\n|                                                                | <b>USD</b>                                                   | CHF                                             |                            | ショ                                                                                        |\n|                                                                | USD                                                          | <b>AUD</b>                                      |                            | $\\bar{z}$<br>市                                                                            |\n|                                                                | <b>USD</b>                                                   | <b>NZD</b>                                      |                            | 3/ 自                                                                                      |\n|                                                                | USD                                                          | CAD                                             |                            | 5/ B                                                                                      |\n|                                                                | <b>USD</b>                                                   | SEK                                             |                            | v)<br>'n                                                                                  |\n|                                                                |                                                              |                                                 | <b>Add Currency Couple</b> |                                                                                           |\n|                                                                |                                                              |                                                 |                            |                                                                                           |\n|                                                                | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!                     |                                                 |                            | Discard All Changes<br>Save                                                               |\n| $\\frac{1}{\\alpha}$                                             | 360T ALIAS X                                                 |                                                 |                            |                                                                                           |\n| 360TAS.Treasurer1, 360T.ALIAS // INT                           |                                                              | <b>ELECTE</b>                                   |                            | Fri, 06: Jul 2018, 08:27:08 GMT // Connected [FFM] .                                      |\n\nFigure 12 Bank Basket: Currency Couple Groups\n\nThe Default Group contains all currency pairs denoted as \\*\\*\\* / \\*\\*\\* for the base and quote currency, respectively.\n\nCurrency Couple Groups can be created, renamed and removed using the standard Configuration Group icons.\n\nWithin a group the user can add currency pairs: Click the Add Currency Couple button. Choosing the ISO code in the drop down list or type the desired currency. Confirm the selection\n\nby clicking the green check mark . Click Save.\n\nImage /page/12/Picture/0 description: {\n \"image\\_description\": \"The image contains text that reads 'User Guide 360T Bank Baskets Configuration'.\"\n}\n\nImage /page/12/Picture/1 description: {\n \"image\\_description\": \"Here are the bounding box detections: [{\\\"box\\_2d\\\": [856, 881, 890, 957], \\\"label\\\": \\\"Save\\\"}, {\\\"box\\_2d\\\": [766, 392, 957, 516], \\\"label\\\": \\\"dropdown\\\"}]\"\n}\n\nFigure 13 Bank Basket: Add Currency Couple\n\n#### 3.1.3 Defining FX Time Period Groups\n\nProducts with varying maturities or tenors may be configured into maturity ranges using the FX Time Period Groups.\n\nOnce created, an FX Time Period Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: Forwards, Swaps, NDF, NDS, Options and Block Trades etc. and (2) Order Spot and Forwards found in the Orders Bank Basket area.\n\nNote: These groups are not relevant for FX Spot and Multi-leg Swaps.\n\nThe Default Group contains all possible maturities denoted as TODAY / UNLIMITED. The values of the Default Group can be modified, but the group cannot be deleted or renamed.\n\nFX Time Period Groups can be created, renamed and removed using the standard Configuration Group icons.\n\nFX Time Periods may be added to each group: Click the Add FX Time Period button. Choose\n\nthe desired time period. Confirm the selection by clicking the green check mark . Click Save.\n\nImage /page/13/Picture/0 description: {\n \"image\\_description\": \"The image contains a user interface for configuring 360T Bank Baskets. It shows options for setting up FX Time Period Groups, including selecting time periods from 'Today' to '3 Months'. The interface includes buttons for creating groups, adding time periods, discarding changes, and saving settings.\"\n}\n\nFigure 14 Bank Basket: Add FX Time Period\n\nThe ability to add discontinuous tenor ranges is possible. For example, two separate periods may be added for TODAY / 1 WEEK and 1 MONTH / 6 MONTHS.\n\nTenors are defined as a range of maturities, with both start and end values included. The same tenors may be used in various groups in order to be used for different sets of rules.\n\n#### 3.1.4 Defining MM Time Period Groups\n\nMM products with varying maturities or tenors may be configured into maturity ranges using the MM Time Period Groups.\n\nOnce created, an MM Time Period Group can be used to simplify rules for interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and Tri Party Repo found in the RFS MM Bank Baskets area.\n\nThe Default Group contains all possible maturities denoted as OVERNIGHT / UNLIMITED. The values of the Default Group can be modified, but the group cannot be deleted or renamed.\n\nMM Time Period Groups can be created, renamed and removed using the standard Configuration Group icons.\n\nMM Time Periods may be added to each group: Clicking the Add MM Time Period button.\n\nSelect the desired time period. Confirm the selection by clicking the green check mark . Click Save.\n\nImage /page/14/Picture/0 description: {\n \"image\\_description\": \"The image shows a user interface for configuring bank baskets in 360T, a Deutsche Börse Group platform. The interface includes options for managing MM Time Period Groups, selecting members for a default group, and adding MM Time Periods. The user is currently selecting a time period from the 'From' dropdown menu, with 'OVERNIGHT' highlighted. The 'To' field is set to 'UNLIMITED'. There are also options to create change requests, discard changes, and save the configuration.\"\n}\n\nFigure 15 Bank Basket: Add MM Time Period\n\nThe ability to add discontinuous tenor ranges is possible. For example, two separate periods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS.\n\nTenors are defined as a range of maturities, with both start and end values included. The same tenors may be used in various groups in order to be used for different sets of rules.\n\n#### 3.1.5 Defining Product Groups\n\nProduct Groups are intended to allow the classification of product types into customized groups.\n\nThis allows setting one single rule for each group of products rather than many rules for individual product types.\n\nImage /page/15/Picture/0 description: {\n \"image\\_description\": \"The image shows a user interface for configuring 360T Bank Baskets. It includes options for RFS Requester, Deal Tracking, and Bridge Administration. The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups. A section titled \\\"Select Member for 'FX Spot and Forward'\\\" is visible, with options for available and selected product types.\"\n}\n\nFigure 16 Bank Basket: Product Groups\n\nProducts can be added or removed from the group without editing the rules themselves.\n\nOnce created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas.\n\nTo add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.\n\nPlease, provide a name for a new group\n\n|  | MM products |\n|--|-------------|\n|--|-------------|\n\n|  | Cancel | Create Group |\n|--|--------|--------------|\n|--|--------|--------------|\n\nFigure 17 Bank Basket: Product Groups Create Group\n\nA default group exists which includes all products. This group cannot be removed or renamed. However, the product types in the group can be altered.\n\nNote: The Default Group contains all product types across RFS, Orders and SEP. However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group.\n\n## 3.2 Provider Groups and Blocked Providers\n\nEach of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers.\n\nImage /page/16/Picture/0 description: {\n \"image\\_description\": \"The image shows a green button with the text \\\"360T\\\" on it. The button has a white outline and a white background. The text is in a stylized font.\"\n}\n\nDEUTSCHE BÖRSE\nGROUP\n\nUser Guide 360T Bank Baskets Configuration\n\n| RFS REQUESTER | DEAL TRACKING | BRIDGE ADMINISTRATION | + |\n|---------------|---------------|-----------------------|---|\n|---------------|---------------|-----------------------|---|\n\n| Preferences | Administration | Help | A A | X |\n|-------------|----------------|------|-----|---|\n|-------------|----------------|------|-----|---|\n\n|  | Q                    |  |  |\n|--|----------------------|--|--|\n|  | 360T ALIAS           |  |  |\n|  | 360T ALIAS Company 1 |  |  |\n|  | 360T ALIAS Company 2 |  |  |\n|  | 360T ALIAS Company 3 |  |  |\n|  | 360T ALIAS Company 4 |  |  |\n|  | 360T ALIAS Company 5 |  |  |\n|  | 360T.ALIAS.Company 6 |  |  |\n\n| Configuration Groups | RFS Bank Baskets | Orders Bank Baskets | SEP Bank Baskets |\n|----------------------|------------------|---------------------|------------------|\n|----------------------|------------------|---------------------|------------------|\n\n| RFS Provider Groups | Blocked RFS Providers | RFS FX Bank Baskets | RFS MM Bank Baskets | RFS Commodity Bank Baskets | RFS Cross Currency Netting Bank Baskets |\n|---------------------|-----------------------|---------------------|---------------------|----------------------------|-----------------------------------------|\n|---------------------|-----------------------|---------------------|---------------------|----------------------------|-----------------------------------------|\n\n| Available Providers | Blocked Providers |\n|---------------------|-------------------|\n| Barclays BARX.DEMO  | 360TBANK.TEST     |\n| BOAL DEMO           |                   |\n| CITIBANK DEMO       |                   |\n| COBA DEMO           |                   |\n| RBC DEMO            |                   |\n| RBS.LND DEMO        |                   |\n| SEB FRA DEMO        |                   |\n| SOCGEN.LND.DEMO     |                   |\n\n| Create Change Request | Discard All Changes | Save |\n|-----------------------|---------------------|------|\n|-----------------------|---------------------|------|\n\n| 360T ALIAS * X | 360T ALIAS Company 1 * X |\n|----------------|--------------------------|\n|----------------|--------------------------|\n\n| 360TAS Treasurer1, 360T ALIAS // INT | Fri, 06. Jul 2018, 11:03:01 GMT // Connected [FFM] |\n|--------------------------------------|----------------------------------------------------|\n|--------------------------------------|----------------------------------------------------|\n\nFigure 18 Bank Basket: Blocked RFS Providers\n\nThe Provider Groups themselves can be edited (values added or removed) without changing a set of rules based on those groups. Providers may also be temporarily blocked from particular request types without affecting the configured Provider Group. A Blocked Provider will remain\n\nin a Provider Group but will appear with a \"blocked\" symbol.\n\nIf a Provider should be removed completely, the relationship should be rejected using the Counterpart Relationship Management tool. In this case, please refer to the relevant user guide.\n\n|  |                                                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                    | Preferences Administration Help AA X                                                                                  |\n|--|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------|\n|  | RFS REQUESTER DEAL TRACKING                                                                                                                                                                                   | + BRIDGE ADMINISTRATION                                                                                                                                                                                                                                                                                                                                                                            |                                                                                                                       |\n|  | Q <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> <                 360T.ALIAS                                                                                                          | Configuration Groups                 RFS Bank Baskets                 Orders Bank Baskets                 SEP Bank Baskets <img alt=\"icon\" src=\"icon_image\"/> RFS Provider Groups                 Blocked RFS Providers                 RFS FX Bank Baskets                 RFS MM Bank Baskets                 RFS Commodity Bank Baskets                 RFS Cross Currency Netting Bank Baskets |                                                                                                                       |\n|  | 360T.ALIAS.Company 1                 360T.ALIAS.Company 2                 360T.ALIAS.Company 3                 360T.ALIAS.Company 4                 360T.ALIAS.Company 5                 360T.ALIAS.Company 6 | Provider Group                 Default Group                 Nordic Banks                 AUD Banks                                                                                                                                                                                                                                                                                                | <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> Create Group |\n|  |                                                                                                                                                                                                               | Select Member for \"Nordic Banks\"                 Available Providers                 Selected Providers                 Barclays BARX.DEMO                 BOAL DEMO                 CITIBANK DEMO                 COBA DEMO                 RBS.LND.DEMO                 SOCGEN.LND DEMO                 TB-HSBC DEMO                                                                             | - x                 360TBANK.TEST                 RBC.DEMO                 SEB FRA.DEMO                               |\n|  | 360TAS Treasurer1, 360T ALIAS // INT                                                                                                                                                                          | Create Change Request                 360T ALIAS x 360T.ALIAS Company 1 x                                                                                                                                                                                                                                                                                                                          | Discard All Changes Save Fri, 06. Jul 2018, 11:06:37 GMT // Connected [FFM] ·                                         |\n\nFigure 19 Bank Basket: RFS Provider Groups\n\nWith the initial activation of the Bank Basket configuration, the Default Group will include all active-relationship providers (where the Counterpart Relationship is \"green-green\").\n\nNote: New Counterparties (newly accepted relationships) will be added to the Default Group.\n\nTo add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save.\n\nAdding or removing providers is done by moving certain banks from \"Available Providers\" to \"Selected Providers\" using the arrow buttons.\n\n## 3.3 Bank Basket Rules\n\n#### 3.3.1 Defining Bank Basket Rules\n\nOnce all configuration groups are in place, individual rules may be defined using the relevant groups for each Bank Basket area (RFS, Orders and SEP).\n\nThe workflow to define bank basket rules is very similar across all Bank Basket areas. The relevant Configuration Groups may vary.\n\nWith the initial activation, all Bank Baskets will contain one single rule denoted with a combination of Any / Default Group for each parameter.\n\nTo add a new rule: Click Add Rule. Select an option from each of the configuration groups\n\nusing the drop down menu. Confirm the selection by clicking the green check mark . Click Save.\n\n|                             |                                                  |                                                                        |                                         |                               | v Preferences v Administration v Help   G AA - C X   |                   |\n|-----------------------------|--------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------|-------------------------------|------------------------------------------------------|-------------------|\n|                             | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b>     | BRIDGE ADMINISTRATION<br>$+$                                           |                                         |                               |                                                      |                   |\n| 合                           | Q 卷 1<br>△ 盒 360T.ALIAS                          | Configuration Groups<br>RFS Bank Baskets                               | Orders Bank Baskets<br>SEP Bank Baskets | $\\partial \\mathcal{E}_k$      |                                                      | $\\Omega \\cap \\Xi$ |\n| $\\mathcal{L}_{\\mathcal{F}}$ | 盒 360T.ALIAS.Company 1                           | Orders Provider Groups<br><b>Blocked Orders Providers</b>              | Orders FX Bank Baskets                  |                               |                                                      |                   |\n| ₿                           | 盒 360T.ALIAS.Company 2<br>意 360T ALIAS Company 3 | POS Product Type                                                       | Currency Couple                         | <b>Time Period</b>            | <b>Bank Basket</b>                                   |                   |\n|                             | 盒 360T.ALIAS.Company 4                           | Default Group<br>$21 - 1$                                              | M/m                                     | Default Group                 | Preferred Limit Order Providers                      | M E               |\n| 凾                           | 盒 360T.ALIAS.Company 5                           | Default Group<br>$-2$                                                  | AUD/***                                 | Default Group                 | Preferred Limit Order Providers                      |                   |\n| -                           | 盒 360T.ALIAS.Company 6                           | 3<br>Any                                                               | Any                                     | Any                           | Default Group                                        |                   |\n| 同                           |                                                  | Default Group<br>$\\hat{\\phantom{a}}$                                   | Default Group<br>$\\checkmark$           | Default Group<br>$\\checkmark$ | Default Group<br>$\\checkmark$                        | Mi                |\n| $\\frac{1}{\\sqrt{2}}$        |                                                  | Any                                                                    |                                         |                               |                                                      | Add Rule          |\n| ශී                          |                                                  | Default Group<br>Spot.                                                 |                                         |                               |                                                      |                   |\n|                             |                                                  | Options<br>Non deliverable<br>Streaming spot<br>Money market<br>Custom |                                         |                               |                                                      |                   |\n| $Q$ D $Q$                   |                                                  | Create Change Request                                                  |                                         |                               | Discard All Changes<br><b>Save</b>                   |                   |\n|                             |                                                  | 360T.ALIAS.Company 1 ' X<br>360T.ALIAS * X                             |                                         |                               |                                                      |                   |\n|                             | 360TAS.Treasurer1, 360TALIAS: // INT             |                                                                        | <b>SECT</b>                             |                               | Fri, 06. Jul 2018, 11:28:21 GMT // Connected [FFM] @ |                   |\n\nFigure 20 Bank Basket: Add Rule\n\nFor each Configuration Group, the user can either explicitly select one of the previously saved groups, \"Any\" or \"Custom\".\n\nThe selection of \"Custom\" in each rule parameter allows to define a criteria in that moment. A predefined Configuration group is not needed.\n\nFor example, if no Currency Couple Group was defined for AUD, but a specific bank basket should be used for all AUD requests, then the rules shown in the figure below can be defined.\n\nImage /page/18/Picture/0 description: {\n \"image\\_description\": \"The image contains the text '360T' in a green, stylized font on a white background. The text appears to be a logo or brand name.\"\n}\n\nUser Guide 360T Bank Baskets Configuration\n\n|                             | <b>DEAL TRACKING</b><br><b>RFS Requester</b>                       |               | $+$<br><b>Bridge Administration</b>             |                                                            |                     |                                   |              |\n|-----------------------------|--------------------------------------------------------------------|---------------|-------------------------------------------------|------------------------------------------------------------|---------------------|-----------------------------------|--------------|\n| 合                           | 激<br>Q<br>$\\mathbb{Z}$                                             |               | Configuration Groups<br><b>RFS Bank Baskets</b> | Orders Bank Baskets                                        | SEP Bank Baskets    |                                   | の心量          |\n|                             | △ 盒 360T.ALIAS                                                     |               | <b>RFS Provider Groups</b>                      | <b>Blocked RFS Providers</b><br><b>RFS Fx Bank Baskets</b> | RFS Mm Bank Baskets | <b>RFS Commodity Bank Baskets</b> | RFS Cross Cr |\n| $\\mathcal{L}_{\\mathcal{F}}$ | <sup> 360T.ALIAS.Company 1</sup>                                   |               |                                                 |                                                            |                     |                                   |              |\n| $\\sim$                      | 360T.ALIAS.Company 2                                               | POS           | Product Type                                    | <b>Currency Couple</b>                                     | <b>Time Period</b>  | <b>Bank Basket</b>                |              |\n| P                           | <b>童 360T.ALIAS.Company 3</b><br><sup>1</sup> 360T.ALIAS.Company 4 | ÷             | Forwards and Swaps                              | Scandies                                                   | Default Group       | Nordic Banks                      | ショ           |\n| $\\frac{1}{\\sqrt{2}}$        | <sup> 360T.ALIAS.Company 5</sup>                                   | $\\frac{1}{2}$ | Any                                             | $M+++$                                                     | Any                 | AUD Banks                         | ショ           |\n| $-$                         |                                                                    | ÷<br>3        | Any                                             | AUD/***                                                    | Any                 | AUD Banks                         | √ □          |\n| ę                           | <b>血 360T.ALIAS.Company 6</b>                                      | ÷<br>4        | Any                                             | Any                                                        | Any                 | Default Group                     | シー           |\n\nFigure 21 Use Custom selection for Currency couples\n\nIn the example above, all RFS requests with base currency AUD (Rule 2) or terms currency AUD (Rule 3) will use the AUD-Group bank basket.\n\nThe tenor can be used in a similar way to determine the banks to which a request will be sent.\n\n#### 3.3.2 Order of the Rules\n\nThe POS (position) column indicates which rules take precedent.\n\nFor each individual request, the Bank Basket will be chosen based on the rule order 1 … n, where 1 is first. If the request meets the criteria found in Rule 1, this rule will be used to define the Bank Basket.\n\nTherefore it is advisable to sort the rules with the most restrictive definition on top.\n\nIn order to move an existing rule up or down in the list, please select the relevant line with the mouse. Drag and drop the rule into the desired position.\n\n|                | <b>Configuration Groups</b>   | <b>RFS Bank Baskets</b>         | <b>Orders Bank Baskets</b> |                        | SEP Bi |\n|----------------|-------------------------------|---------------------------------|----------------------------|------------------------|--------|\n|                | <b>Orders Provider Groups</b> | <b>Blocked Orders Providers</b> |                            | <b>Orders Fx Bank</b>  |        |\n|                |                               |                                 |                            |                        |        |\n| POS            | Product Type                  |                                 |                            | <b>Currency Couple</b> |        |\n| 1              | Default Group                 |                                 |                            | ***/AUD                |        |\n| $\\overline{2}$ | Default Group                 |                                 |                            | AUD/***                |        |\n\nFigure 22 Bank Basket: Order of Rules\n\n## 3.4 Note on Supersonic\n\nThe possibility to configure currency Bank Baskets for Supersonic is not available yet. Bank Basket selection is configured directly in the Supersonic interface by defining SEP Contributors for each spot currency pair.\n\nTherefore, the SEP Bank Baskets are currently only used to block SEP providers if needed.\n\n## 4 BANK BASKETS EVALUATOR TOOL\n\nThe Bank Baskets Evaluator Tool may assist the user in identifying which rule applies to a particular type of request.\n\nThe tool can be accessed from the Bridge Administration Homepage.\n\nImage /page/19/Picture/0 description: {\n \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The main area of the screen displays the 'Administration Start' page, which includes sections for 'Configurations' and 'Actions'. Under 'Configurations', there are icons for 'Regulatory Data' and 'Bank Baskets'. Under 'Actions', there are icons for 'Change Request', 'Wizards', and 'Evaluator Tools'. The 'Evaluator Tools' icon is highlighted with a red dashed border. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with options for 'Preferences', 'Administration', and 'Help'. The bottom of the screen displays information about the user's connection and the date and time.\"\n}\n\nFigure 23 Evaluator Tool Quick Link\n\nThe tool may also be accessed using the Bank Basket Evaluator Tool icon located next to the Configuration Data Tabs.\n\n|  |  | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b>           | <b>BRIDGE ADMINISTRATION</b>                                                                                                                                      | Preferences Administration Help AA - X |                                                      |\n|--|--|--------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|------------------------------------------------------|\n|  |  | Q                   360T.ALIAS<br>360T ALIAS Company 1 | Configuration Groups                 RFS Bank Baskets                 Orders Bank Baskets                 SEP Bank Baskets                                        |                                        |                                                      |\n|  |  | 360T.ALIAS Company 2<br>360T.ALIAS Company 3           | Currency Groups                 Currency Couple Groups                 FX Time Period Groups                 MM Time Period Groups                 Product Groups |                                        |                                                      |\n|  |  | 360T.ALIAS Company 4<br>360T.ALIAS Company 5           | Currency Group                 Default Group                                                                                                                      |                                        |                                                      |\n|  |  | 360T ALIAS Company 6                                   |                                                                                                                                                                   | Create Group                           |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        |                                                                                                                                                                   |                                        |                                                      |\n|  |  |                                                        | Create Change Request                                                                                                                                             | Discard All Changes                    | Save                                                 |\n|  |  |                                                        | 360T ALIAS * X 360T ALIAS Company 1 * X                                                                                                                           |                                        |                                                      |\n|  |  | 360TAS Treasurer1, 360T ALIAS // INT                   |                                                                                                                                                                   |                                        | Fri, 06. Jul 2018, 12:04:09 GMT // Connected [FFM] . |\n\nFigure 24 Evaluator Tool icon\n\nBoth methods will require the user to identify the desired company to evaluate that company's Bank Basket. If using the icon, the system will take the company Bank Basket based on the current active tab.\n\nEnter the desired parameters for a particular type of request. Click \"Find Bank Baskets Rule\".\n\nPlease enter product details\n\n| Product Type:    | Fx Spot   |\n|------------------|-----------|\n| Currency Couple: | EUR / USD |\n\nFind Bank Baskets RuleFigure 25 Evaluator Tool\n\nThe system will jump to and highlight the relevant rule.\n\n|                                                                | <b>DEAL TRACKING</b><br><b>RFS REQUESTER</b>          | $+$<br><b>BRIDGE ADMINISTRATION</b>                             |                                            |                            | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $ \\Box$ X    |          |\n|----------------------------------------------------------------|-------------------------------------------------------|-----------------------------------------------------------------|--------------------------------------------|----------------------------|-----------------------------------------------------------------------------|----------|\n| 合                                                              | Q 豪<br>12<br>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>Configuration Groups</b><br><b>RFS Bank Baskets</b>          | Orders Bank Baskets<br>SEP Bank Baskets    | $\\sigma$                   |                                                                             | のの言      |\n|                                                                | △ 盒 360T.ALIAS                                        | RFS Provider Groups<br><b>Blocked RFS Providers</b>             | RFS FX Bank Baskets<br>RFS MM Bank Baskets | RFS Commodity Bank Baskets | RFS Cross Currency Netting Bank Baskets                                     |          |\n| $\\mathcal{L}$                                                  | 盒 360T.ALIAS.Company 1                                |                                                                 |                                            |                            |                                                                             |          |\n| $\\qquad \\qquad \\boxdot \\qquad$                                 | 盒 360T.ALIAS.Company 2<br>盒 360T.ALIAS.Company 3      | POS Product Type                                                | Currency Couple                            | <b>Time Period</b>         | <b>Bank Basket</b>                                                          |          |\n| $\\frac{d}{d}$                                                  | 盒 360T.ALIAS.Company 4                                | : 1 Forwards and Swaps                                          | Scandies                                   | Default Group              | Nordic Banks                                                                | ショ       |\n|                                                                | 盒 360T ALIAS.Company 5                                | $2$ Any                                                         | ***/AUD                                    | Any                        | AUD Banks                                                                   | ジョ       |\n| -                                                              | 盒 360T.ALIAS.Company 6                                | $\\frac{1}{2}$ 3 Any                                             | AUD/***                                    | Any                        | AUD Banks                                                                   | ジョ       |\n| 厚                                                              |                                                       | 4 Any                                                           | Any                                        | Any                        | Default Group                                                               | $ V $ (  |\n| $\\frac{1}{\\sqrt{2}}$                                           |                                                       |                                                                 |                                            |                            |                                                                             | Add Rule |\n|                                                                |                                                       |                                                                 |                                            |                            |                                                                             |          |\n| $\\begin{array}{c}\\n\\bullet \\\\ \\bullet \\\\ \\bullet\\n\\end{array}$ | 360TAS.Treasurer1, 360T.ALIAS // INT                  | Create Change Request<br>360T ALIAS Company 1 X<br>360T.ALIAS X | <b>EECT</b>                                |                            | Discard All Changes<br>Fri, 06. Jul 2018, 12:10:31 GMT // Connected [FFM] . | Save     |\n\nFigure 26 Evaluator Tool: Highlighted Rule\n\n## 5 CONTACT 360T\n\nImage /page/21/Picture/2 description: {\n \"image\\_description\": \"The image shows a logo with the number '360' in a stylized font, followed by a symbol resembling two arrows pointing towards each other within a square, and then the letter 'T'. The logo is primarily green with white accents and is set against a white background.\"\n}\n\n#### Global Support\n\nPhone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL>\n\n#### Germany\n\n360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main\n\nPhone: +49 69 900289-0\n\n### Middle East Asia Pacific\n\n## United Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai\n\nPhone: +971 4 431 5134\n\n### EMEA Americas\n\n## USA 360 Trading Networks Inc. 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900\n\n## Singapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "metadata": {"lang": "en"}}]