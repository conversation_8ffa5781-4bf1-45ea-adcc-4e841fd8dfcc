{"id": "1", "metadata": {"lang": "en"}, "original_text": "## BANK BASKET CONFIGURATION (BRIDGE ADMINISTRATION) Image /page/0/Picture/1 description: { \"image\\_description\": \"The image shows a logo for \\\"360T\\\", which is a green rectangle with rounded corners. The text \\\"360\\\" is in white with a green outline, and the \\\"T\\\" is also in white with a green outline. There are two arrows pointing towards each other in the middle of the \\\"0\\\" in \\\"360\\\".\" } # TEX MULTIDEALER TRADING SYSTEM USER GUIDE 360T BRIDGE ADMINISTRATION: ENHANCED BANK BASKET CONFIGURATION © 360 TREASURY SYSTEMS AG, 2019 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG ## CONTENTS | 1 | | INTRODUCTION | 4 | |---|-------|---------------------------------------|----| | 2 | | GETTING STARTED | 4 | | 3 | | CONFIGURATION OF BANK BASKETS | 7 | | | 3.1 | SETTING UP CONFIGURATION GROUPS | 8 | | | 3.1.1 | Defining Currency Groups | 9 | | | 3.1.2 | Defining Currency Couple Groups | 11 | | | 3.1.3 | Defining FX Time Period Groups | 13 | | | 3.1.4 | Defining MM Time Period Groups | 14 | | | 3.1.5 | Defining Product Groups | 15 | | | 3.2 | PROVIDER GROUPS AND BLOCKED PROVIDERS | 16 | | | 3.3 | BANK BASKET RULES | 18 | | | 3.3.1 | Defining Bank Basket Rules | 18 | | | 3.3.2 | Order of the Rules | 19 | | | 3.4 | NOTE ON SUPERSONIC | 19 | | 4 | | BANK BASKETS EVALUATOR TOOL | 19 | | 5 | | CONTACT 360T | 22 | ## TABLE OF FIGURES | Figure 1 Header Bar | 4 | |-----------------------------------------------------------------|----| | Figure 2 Bridge Administration: Homepage | 5 | | Figure 3 Bank Basket: Start page | 5 | | Figure 4 Bank Basket: Configuration for a selected legal entity | 6 | | Figure 5 Bank Basket: Configuration: Live Audit Log | 7 | | Figure 6 Bank Basket: Configuration Data Tabs | 8 | | Figure 7 Bank Basket: Currency Groups | 9 | | Figure 8 Bank Basket: RFS MM Bank Baskets | 10 | | Figure 9 Bank Basket: Currency Groups Default Group | 10 | | Figure 10 Bank Basket: Currency Groups Create Group | 11 | | Figure 11 Bank Basket: Configured Currency Group | 11 | | Figure 12 Bank Basket: Currency Couple Groups | 12 | | Figure 13 Bank Basket: Add Currency Couple | 13 | | Figure 14 Bank Basket: Add FX Time Period | 14 | | Figure 15 Bank Basket: Add MM Time Period | 15 | | Figure 16 Bank Basket: Product Groups | 16 | | Figure 17 Bank Basket: Product Groups Create Group | 16 | | Figure 18 Bank Basket: Blocked RFS Providers | 17 | | Figure 19 Bank Basket: RFS Provider Groups | 17 | | Figure 20 Bank Basket: Add Rule 18 | | |---------------------------------------------------------|--| | Figure 21 Use Custom selection for Currency couples 19 | | | Figure 22 Bank Basket: Order of Rules 19 | | | Figure 23 Evaluator Tool Quick Link 20 | | | Figure 24 Evaluator Tool icon 20 | | | Figure 24 Evaluator Tool 21 | ", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The image shows a logo for 360T.", "Entity": ["image", "logo", "360T"]}, {"Event": "The logo is a green rectangle with rounded corners.", "Entity": ["logo", "green rectangle", "rounded corners"]}, {"Event": "The text 360 is in white with a green outline.", "Entity": ["text", "360", "white", "green outline"]}, {"Event": "The T is in white with a green outline.", "Entity": ["T", "white", "green outline"]}, {"Event": "Two arrows point towards each other in the middle of the 0 in 360.", "Entity": ["arrows", "0", "360"]}, {"Event": "This file contains proprietary and confidential information including trade secrets.", "Entity": ["file", "proprietary information", "confidential information", "trade secrets"]}, {"Event": "The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.", "Entity": ["information", "third party", "written approval", "360 TREASURY SYSTEMS AG"]}, {"Event": "360 TREASURY SYSTEMS AG holds the copyright for 2019.", "Entity": ["360 TREASURY SYSTEMS AG", "copyright", "2019"]}], "event_relation_dict": [{"Head": "The introduction is presented.", "Relation": "before", "Tail": "Getting started information is provided."}, {"Head": "Getting started information is provided.", "Relation": "before", "Tail": "Bank baskets are configured."}, {"Head": "Bank baskets are configured.", "Relation": "before", "Tail": "The bank baskets evaluator tool is used."}, {"Head": "The bank baskets evaluator tool is used.", "Relation": "before", "Tail": "Contact information for 360T is available."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The image shows a logo for 360T.\", \"Entity\": [\"image\", \"logo\", \"360T\"]}, {\"Event\": \"The logo is a green rectangle with rounded corners.\", \"Entity\": [\"logo\", \"green rectangle\", \"rounded corners\"]}, {\"Event\": \"The text 360 is in white with a green outline.\", \"Entity\": [\"text\", \"360\", \"white\", \"green outline\"]}, {\"Event\": \"The T is in white with a green outline.\", \"Entity\": [\"T\", \"white\", \"green outline\"]}, {\"Event\": \"Two arrows point towards each other in the middle of the 0 in 360.\", \"Entity\": [\"arrows\", \"0\", \"360\"]}, {\"Event\": \"This file contains proprietary and confidential information including trade secrets.\", \"Entity\": [\"file\", \"proprietary information\", \"confidential information\", \"trade secrets\"]}, {\"Event\": \"The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.\", \"Entity\": [\"information\", \"third party\", \"written approval\", \"360 TREASURY SYSTEMS AG\"]}, {\"Event\": \"360 TREASURY SYSTEMS AG holds the copyright for 2019.\", \"Entity\": [\"360 TREASURY SYSTEMS AG\", \"copyright\", \"2019\"]}]", "output_stage_three": "[{\"Head\": \"The introduction is presented.\", \"Relation\": \"before\", \"Tail\": \"Getting started information is provided.\"}, {\"Head\": \"Getting started information is provided.\", \"Relation\": \"before\", \"Tail\": \"Bank baskets are configured.\"}, {\"Head\": \"Bank baskets are configured.\", \"Relation\": \"before\", \"Tail\": \"The bank baskets evaluator tool is used.\"}, {\"Head\": \"The bank baskets evaluator tool is used.\", \"Relation\": \"before\", \"Tail\": \"Contact information for 360T is available.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 165, "time": 1.0}, "usage_stage_three": {"completion_tokens": 68, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "| | Figure 26 Evaluator Tool: Highlighted Rule 21 | | ## 1 INTRODUCTION This user manual describes the Bank Basket feature of the 360T Bridge Administration tool. The Bank Basket feature has been enhanced to provide improved rule management capabilities, including the introduction of configuration groups based on currency, currency couple, time period and product(s); configuration of separate baskets by request type (RFS, Order, SEP); as well as the ability to apply and remove temporary blocks without affecting the configured rules or counterpart relationship(s). #### Please note: The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications. Only users with corresponding user rights are able to administer the company's Bank Baskets. <NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights. ## 2 GETTING STARTED The Bank Basket configuration is found within the Bridge Administration tool. Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application. | | | | $\\vee$ Preferences<br>V Administration | $\\times$ Help<br>$\\Box$<br>A <sub>A</sub><br>$\\mathbb{X}$ | |-----------------------------------------------------------------------------|-------------------------------------------------------------------------------------|----------------------------------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------| | > Bridge Administration | | | | X | | <b>SCIENCES</b> | | | <b>SCIENS</b> | | | <b>UNIT SIDEN</b><br>$^{117}890$<br><sup>⊞7</sup> 92α<br>Spot // 21.11.2017 | <b><i>STATISTICS</i></b><br><b>POST MARK</b><br>1327<br>##336<br>Spot // 21 11.2017 | <b>STATISTICS</b><br><b>START CORPORA</b><br>084<br>0.55<br>5001//21112017 | <b>Life of Life and</b><br>889184<br>0.994<br>93.<br>Spot // 2111 2017 | | | 11703<br><b>ILFOG.</b> | 132.2 d | 08900 mm<br><b>TIDO AT TELL</b> | 0.99 ft J Lune<br>0994 T | | Figure 1 Header Bar The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user. A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage. Image /page/4/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user guide. The screen displays the 'Administration Start' page with options for 'Regulatory Data', 'Bank Baskets', 'Change Request', 'Wizards', and 'Evaluator Tools'. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with preferences and help options. The bottom of the screen display", "entity_relation_dict": [{"Head": "user manual", "Relation": "describes", "Tail": "Bank Basket feature"}, {"Head": "Bank Basket feature", "Relation": "is part of", "Tail": "360T Bridge Administration tool"}, {"Head": "Bank Basket feature", "Relation": "provides", "Tail": "improved rule management capabilities"}, {"Head": "improved rule management capabilities", "Relation": "include", "Tail": "configuration groups"}, {"Head": "configuration groups", "Relation": "are based on", "Tail": "currency"}, {"Head": "configuration groups", "Relation": "are based on", "Tail": "currency couple"}, {"Head": "configuration groups", "Relation": "are based on", "Tail": "time period"}, {"Head": "configuration groups", "Relation": "are based on", "Tail": "product(s)"}, {"Head": "improved rule management capabilities", "Relation": "include", "Tail": "configuration of separate baskets"}, {"Head": "separate baskets", "Relation": "are configured by", "Tail": "request type"}], "event_entity_relation_dict": [{"Event": "This user manual describes the Bank Basket feature.", "Entity": ["This user manual", "Bank Basket feature", "360T Bridge Administration tool"]}, {"Event": "The Bank Basket feature has been enhanced to provide improved rule management capabilities.", "Entity": ["Bank Basket feature", "rule management capabilities", "configuration groups", "currency", "currency couple", "time period", "product(s)", "separate baskets", "request type", "RFS", "Order", "SEP", "temporary blocks", "configured rules", "counterpart relationship(s)"]}, {"Event": "The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.", "Entity": ["360T enhanced Bank Basket feature", "RFS request types", "order request types", "entities", "EMS application", "Bridge applications"]}, {"Event": "Only users with corresponding user rights are able to administer the company's Bank Baskets.", "Entity": ["users", "user rights", "company", "Bank Baskets"]}, {"Event": "<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.", "Entity": ["<EMAIL>", "customer relationship manager", "administrative rights"]}, {"Event": "The Bank Basket configuration is found within the Bridge Administration tool.", "Entity": ["Bank Basket configuration", "Bridge Administration tool"]}, {"Event": "Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application.", "Entity": ["Bridge Administration", "menu option \"Administration\"", "screen header", "Bridge application"]}, {"Event": "The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.", "Entity": ["Bridge Administration feature", "homepage", "shortcuts", "configuration tools", "user"]}, {"Event": "A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.", "Entity": ["quick navigation toolbar", "active homepage icon", "homepage"]}], "event_relation_dict": [], "output_stage_one": "[{\"Head\": \"user manual\", \"Relation\": \"describes\", \"Tail\": \"Bank Basket feature\"}, {\"Head\": \"Bank Basket feature\", \"Relation\": \"is part of\", \"Tail\": \"360T Bridge Administration tool\"}, {\"Head\": \"Bank Basket feature\", \"Relation\": \"provides\", \"Tail\": \"improved rule management capabilities\"}, {\"Head\": \"improved rule management capabilities\", \"Relation\": \"include\", \"Tail\": \"configuration groups\"}, {\"Head\": \"configuration groups\", \"Relation\": \"are based on\", \"Tail\": \"currency\"}, {\"Head\": \"configuration groups\", \"Relation\": \"are based on\", \"Tail\": \"currency couple\"}, {\"Head\": \"configuration groups\", \"Relation\": \"are based on\", \"Tail\": \"time period\"}, {\"Head\": \"configuration groups\", \"Relation\": \"are based on\", \"Tail\": \"product(s)\"}, {\"Head\": \"improved rule management capabilities\", \"Relation\": \"include\", \"Tail\": \"configuration of separate baskets\"}, {\"Head\": \"separate baskets\", \"Relation\": \"are configured by\", \"Tail\": \"request type\"}]", "output_stage_two": "[{\"Event\": \"This user manual describes the Bank Basket feature.\", \"Entity\": [\"This user manual\", \"Bank Basket feature\", \"360T Bridge Administration tool\"]}, {\"Event\": \"The Bank Basket feature has been enhanced to provide improved rule management capabilities.\", \"Entity\": [\"Bank Basket feature\", \"rule management capabilities\", \"configuration groups\", \"currency\", \"currency couple\", \"time period\", \"product(s)\", \"separate baskets\", \"request type\", \"RFS\", \"Order\", \"SEP\", \"temporary blocks\", \"configured rules\", \"counterpart relationship(s)\"]}, {\"Event\": \"The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.\", \"Entity\": [\"360T enhanced Bank Basket feature\", \"RFS request types\", \"order request types\", \"entities\", \"EMS application\", \"Bridge applications\"]}, {\"Event\": \"Only users with corresponding user rights are able to administer the company's Bank Baskets.\", \"Entity\": [\"users\", \"user rights\", \"company\", \"Bank Baskets\"]}, {\"Event\": \"<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.\", \"Entity\": [\"<EMAIL>\", \"customer relationship manager\", \"administrative rights\"]}, {\"Event\": \"The Bank Basket configuration is found within the Bridge Administration tool.\", \"Entity\": [\"Bank Basket configuration\", \"Bridge Administration tool\"]}, {\"Event\": \"Bridge Administration can be accessed via the menu option \\\"Administration\\\" in the screen header of the Bridge application.\", \"Entity\": [\"Bridge Administration\", \"menu option \\\"Administration\\\"\", \"screen header\", \"Bridge application\"]}, {\"Event\": \"The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.\", \"Entity\": [\"Bridge Administration feature\", \"homepage\", \"shortcuts\", \"configuration tools\", \"user\"]}, {\"Event\": \"A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.\", \"Entity\": [\"quick navigation toolbar\", \"active homepage icon\", \"homepage\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 123, "time": 1.0}, "usage_stage_two": {"completion_tokens": 294, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "s system information and connection status.\" } Figure 2 Bridge Administration: Homepage The \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree. Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity. | | | | | | | | | Preferences | Administration | Help | A A | |--|--|----------------------|----------------------|------------------------------|---|--|--|-------------|----------------|------|-----| | | | <b>RFS Requester</b> | <b>DEAL TRACKING</b> | <b>Bridge Administration</b> | + | | | | | | | Q 360T.ALIAS 360T.ALIAS.Company 1 360T.ALIAS.Company 2 360T.ALIAS.Company 3 360T.ALIAS.Company 4 360T.ALIAS.Company 5 360T.ALIAS.Company 6 >> No Individuals/Institutions are selected | 360TAS Treasurer 1, 360T ALIAS // INT | Mi, 25. Apr 2018, 14:42:22 GMT // Connected | |---------------------------------------|---------------------------------------------| |---------------------------------------|---------------------------------------------| Figure 3 Bank Basket: Start page The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity. It is possible to open multiple forms/sheets at a time. The selected item will be highlighted as an active task inside the taskbar. Image /page/5/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The interface includes a navigation menu on the left, a main content area in the center, and a header with user preferences and administration options. The navigation menu lists several 360T.ALIAS companies. The main content area displays currency group settings, with options to create and manage currency groups. The header includes tabs for different configuration groups, such as RFS Bank Baskets, Orders Bank Baskets, and SEP Bank Baskets.\" } Figure 4 Bank Basket: Configuration for a selected legal entity A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel: - Search : A search field will open and the user can type in an alphanumeric value in order to find the desired institution. - Scroll from source : This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel. Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source. - Show individuals view toggle : This icon is deactivated when using the Bank Basket configuration. For other configuration tools the toggle option allows the user to display only individuals in the navigation panel. - Show institutions view toggle : This icon is deactivated when using t", "entity_relation_dict": [{"Head": "Bank Baskets quick link", "Relation": "opens", "Tail": "navigation panel"}, {"Head": "navigation panel", "Relation": "contains", "Tail": "institution tree"}, {"Head": "institution tree", "Relation": "includes", "Tail": "single TEX entity"}, {"Head": "institution tree", "Relation": "includes", "Tail": "TEX main entity"}, {"Head": "TEX main entity", "Relation": "configures", "Tail": "trade-as entities"}, {"Head": "TEX main entity", "Relation": "configures", "Tail": "trade-on-behalf entities"}, {"Head": "TEX main entity", "Relation": "configures", "Tail": "ITEX entities"}, {"Head": "single-click", "Relation": "selects", "Tail": "institution"}, {"Head": "single-click", "Relation": "opens", "Tail": "new form/sheet"}, {"Head": "new form/sheet", "Relation": "displays", "Tail": "Bank Basket configuration details"}, {"Head": "Bank Basket configuration details", "Relation": "pertain to", "Tail": "selected institution"}], "event_entity_relation_dict": [{"Event": "The \"Bank Baskets\" quick link opens a navigation panel.", "Entity": ["Bank Baskets quick link", "navigation panel"]}, {"Event": "The navigation panel contains an institution tree.", "Entity": ["navigation panel", "institution tree"]}, {"Event": "The tree may include a single TEX entity.", "Entity": ["tree", "TEX entity"]}, {"Event": "The tree may include a TEX main entity.", "Entity": ["tree", "TEX main entity"]}, {"Event": "Trade-as entities are configured under the main entity.", "Entity": ["trade-as entities", "main entity"]}, {"Event": "Trade-on-behalf entities are configured under the main entity.", "Entity": ["trade-on-behalf entities", "main entity"]}, {"Event": "ITEX entities are configured under the main entity.", "Entity": ["ITEX entities", "main entity"]}, {"Event": "The selection of an institution is done by single-click within the institution tree.", "Entity": ["selection", "institution", "single-click", "institution tree"]}, {"Event": "A single-click within the institution tree opens a new form/sheet.", "Entity": ["single-click", "institution tree", "new form/sheet"]}, {"Event": "The new form/sheet contains the Bank Basket configuration details of that entity.", "Entity": ["new form/sheet", "Bank Basket configuration details", "entity"]}, {"Event": "It is possible to open multiple forms/sheets at a time.", "Entity": ["multiple forms/sheets"]}, {"Event": "The selected item will be highlighted as an active task.", "Entity": ["selected item", "active task"]}, {"Event": "The selected item will be highlighted inside the taskbar.", "Entity": ["selected item", "taskbar"]}, {"Event": "A set of icons is placed at the top of the navigation panel.", "Entity": ["set of icons", "navigation panel"]}, {"Event": "The icons can be activated by a single-click.", "Entity": ["icons", "single-click"]}, {"Event": "The icons can be deactivated by a single-click.", "Entity": ["icons", "single-click"]}, {"Event": "A search field will open.", "Entity": ["search field"]}, {"Event": "The user can type in an alphanumeric value.", "Entity": ["user", "alphanumeric value"]}, {"Event": "The user can find the desired institution.", "Entity": ["user", "desired institution"]}, {"Event": "The \"Scroll from source\" feature can be used.", "Entity": ["Scroll from source feature"]}, {"Event": "The user desires to find the currently active task/sheet.", "Entity": ["user", "currently active task/sheet"]}, {"Event": "The currently active task/sheet is in the navigation panel.", "Entity": ["currently active task/sheet", "navigation panel"]}, {"Event": "Jumping to the selected tree item is possible.", "Entity": ["selected tree item"]}, {"Event": "Jumping to the active institution in the taskbar is possible.", "Entity": ["active institution", "taskbar"]}, {"Event": "Jumping is possible when clicking \"scroll from source\".", "Entity": ["scroll from source"]}, {"Event": "The \"Show individuals view toggle\" icon is deactivated.", "Entity": ["Show individuals view toggle icon"]}, {"Event": "The \"Show individuals view toggle\" icon is deactivated when using the Bank Basket configuration.", "Entity": ["Show individuals view toggle icon", "Bank Basket configuration"]}, {"Event": "The toggle option allows the user to display only individuals.", "Entity": ["toggle option", "user", "individuals"]}, {"Event": "The individuals are displayed in the navigation panel.", "Entity": ["individuals", "navigation panel"]}, {"Event": "The toggle option is for other configuration tools.", "Entity": ["toggle option", "other configuration tools"]}, {"Event": "The \"Show institutions view toggle\" icon is deactivated.", "Entity": ["Show institutions view toggle icon"]}], "event_relation_dict": [{"Head": "An institution is selected by a single-click.", "Relation": "as a result", "Tail": "A new form or sheet opens with the Bank Basket configuration details."}, {"Head": "An item is selected.", "Relation": "as a result", "Tail": "The selected item is highlighted as an active task inside the taskbar."}, {"Head": "The search icon is activated.", "Relation": "as a result", "Tail": "A search field will open."}, {"Head": "The user types an alphanumeric value.", "Relation": "because", "Tail": "The user finds the desired institution."}, {"Head": "The user clicks scroll from source.", "Relation": "as a result", "Tail": "Jumping to the selected tree item is possible."}, {"Head": "The Bank Basket configuration is used.", "Relation": "because", "Tail": "The 'Show individuals view toggle' icon is deactivated."}], "output_stage_one": "[{\"Head\": \"Bank Baskets quick link\", \"Relation\": \"opens\", \"Tail\": \"navigation panel\"}, {\"Head\": \"navigation panel\", \"Relation\": \"contains\", \"Tail\": \"institution tree\"}, {\"Head\": \"institution tree\", \"Relation\": \"includes\", \"Tail\": \"single TEX entity\"}, {\"Head\": \"institution tree\", \"Relation\": \"includes\", \"Tail\": \"TEX main entity\"}, {\"Head\": \"TEX main entity\", \"Relation\": \"configures\", \"Tail\": \"trade-as entities\"}, {\"Head\": \"TEX main entity\", \"Relation\": \"configures\", \"Tail\": \"trade-on-behalf entities\"}, {\"Head\": \"TEX main entity\", \"Relation\": \"configures\", \"Tail\": \"ITEX entities\"}, {\"Head\": \"single-click\", \"Relation\": \"selects\", \"Tail\": \"institution\"}, {\"Head\": \"single-click\", \"Relation\": \"opens\", \"Tail\": \"new form/sheet\"}, {\"Head\": \"new form/sheet\", \"Relation\": \"displays\", \"Tail\": \"Bank Basket configuration details\"}, {\"Head\": \"Bank Basket configuration details\", \"Relation\": \"pertain to\", \"Tail\": \"selected institution\"}]", "output_stage_two": "[{\"Event\": \"The \\\"Bank Baskets\\\" quick link opens a navigation panel.\", \"Entity\": [\"Bank Baskets quick link\", \"navigation panel\"]}, {\"Event\": \"The navigation panel contains an institution tree.\", \"Entity\": [\"navigation panel\", \"institution tree\"]}, {\"Event\": \"The tree may include a single TEX entity.\", \"Entity\": [\"tree\", \"TEX entity\"]}, {\"Event\": \"The tree may include a TEX main entity.\", \"Entity\": [\"tree\", \"TEX main entity\"]}, {\"Event\": \"Trade-as entities are configured under the main entity.\", \"Entity\": [\"trade-as entities\", \"main entity\"]}, {\"Event\": \"Trade-on-behalf entities are configured under the main entity.\", \"Entity\": [\"trade-on-behalf entities\", \"main entity\"]}, {\"Event\": \"ITEX entities are configured under the main entity.\", \"Entity\": [\"ITEX entities\", \"main entity\"]}, {\"Event\": \"The selection of an institution is done by single-click within the institution tree.\", \"Entity\": [\"selection\", \"institution\", \"single-click\", \"institution tree\"]}, {\"Event\": \"A single-click within the institution tree opens a new form/sheet.\", \"Entity\": [\"single-click\", \"institution tree\", \"new form/sheet\"]}, {\"Event\": \"The new form/sheet contains the Bank Basket configuration details of that entity.\", \"Entity\": [\"new form/sheet\", \"Bank Basket configuration details\", \"entity\"]}, {\"Event\": \"It is possible to open multiple forms/sheets at a time.\", \"Entity\": [\"multiple forms/sheets\"]}, {\"Event\": \"The selected item will be highlighted as an active task.\", \"Entity\": [\"selected item\", \"active task\"]}, {\"Event\": \"The selected item will be highlighted inside the taskbar.\", \"Entity\": [\"selected item\", \"taskbar\"]}, {\"Event\": \"A set of icons is placed at the top of the navigation panel.\", \"Entity\": [\"set of icons\", \"navigation panel\"]}, {\"Event\": \"The icons can be activated by a single-click.\", \"Entity\": [\"icons\", \"single-click\"]}, {\"Event\": \"The icons can be deactivated by a single-click.\", \"Entity\": [\"icons\", \"single-click\"]}, {\"Event\": \"A search field will open.\", \"Entity\": [\"search field\"]}, {\"Event\": \"The user can type in an alphanumeric value.\", \"Entity\": [\"user\", \"alphanumeric value\"]}, {\"Event\": \"The user can find the desired institution.\", \"Entity\": [\"user\", \"desired institution\"]}, {\"Event\": \"The \\\"Scroll from source\\\" feature can be used.\", \"Entity\": [\"Scroll from source feature\"]}, {\"Event\": \"The user desires to find the currently active task/sheet.\", \"Entity\": [\"user\", \"currently active task/sheet\"]}, {\"Event\": \"The currently active task/sheet is in the navigation panel.\", \"Entity\": [\"currently active task/sheet\", \"navigation panel\"]}, {\"Event\": \"Jumping to the selected tree item is possible.\", \"Entity\": [\"selected tree item\"]}, {\"Event\": \"Jumping to the active institution in the taskbar is possible.\", \"Entity\": [\"active institution\", \"taskbar\"]}, {\"Event\": \"Jumping is possible when clicking \\\"scroll from source\\\".\", \"Entity\": [\"scroll from source\"]}, {\"Event\": \"The \\\"Show individuals view toggle\\\" icon is deactivated.\", \"Entity\": [\"Show individuals view toggle icon\"]}, {\"Event\": \"The \\\"Show individuals view toggle\\\" icon is deactivated when using the Bank Basket configuration.\", \"Entity\": [\"Show individuals view toggle icon\", \"Bank Basket configuration\"]}, {\"Event\": \"The toggle option allows the user to display only individuals.\", \"Entity\": [\"toggle option\", \"user\", \"individuals\"]}, {\"Event\": \"The individuals are displayed in the navigation panel.\", \"Entity\": [\"individuals\", \"navigation panel\"]}, {\"Event\": \"The toggle option is for other configuration tools.\", \"Entity\": [\"toggle option\", \"other configuration tools\"]}, {\"Event\": \"The \\\"Show institutions view toggle\\\" icon is deactivated.\", \"Entity\": [\"Show institutions view toggle icon\"]}]", "output_stage_three": "[{\"Head\": \"An institution is selected by a single-click.\", \"Relation\": \"as a result\", \"Tail\": \"A new form or sheet opens with the Bank Basket configuration details.\"}, {\"Head\": \"An item is selected.\", \"Relation\": \"as a result\", \"Tail\": \"The selected item is highlighted as an active task inside the taskbar.\"}, {\"Head\": \"The search icon is activated.\", \"Relation\": \"as a result\", \"Tail\": \"A search field will open.\"}, {\"Head\": \"The user types an alphanumeric value.\", \"Relation\": \"because\", \"Tail\": \"The user finds the desired institution.\"}, {\"Head\": \"The user clicks scroll from source.\", \"Relation\": \"as a result\", \"Tail\": \"Jumping to the selected tree item is possible.\"}, {\"Head\": \"The Bank Basket configuration is used.\", \"Relation\": \"because\", \"Tail\": \"The 'Show individuals view toggle' icon is deactivated.\"}]", "usage_stage_one": {"completion_tokens": 126, "time": 1.0}, "usage_stage_two": {"completion_tokens": 529, "time": 1.0}, "usage_stage_three": {"completion_tokens": 133, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "he Bank Basket configuration. For other configuration tools the toggle option allows the user to display only institutions in the navigation panel. The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel. Each entity tab has a Live Audit Log which tracks all unsaved changes. Individual unsaved changes can be reverted by clicking on the arrow icon. Clicking on the \"Discard all changes\" button will revert all unsaved changes. Image /page/6/Picture/0 description: { \"image\\_description\": \"The image contains the text 'User Guide 360T Bank Baskets Configuration'. The text appears to be the title or heading of a document or guide.\" } | | 9 ※ 上 | | | | | | Live Audit Log | 目<br>$\\Omega$ | |-----------------------------|------------------------------------------|---------------------------------------------------------|-----------------------------------|------------------------------------------|---|------------|------------------|---------------| | $\\mathcal{L}_{\\mathcal{F}}$ | へ 盒 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | <b>Currency Groups</b><br><b>Currency Couple Groups</b> | FX Time Period Groups | MM Time Period ( ) | | Target | Event Name | | | | 直 360T.ALIAS.Company 2 | | | | | 360T.ALIAS | Currency Added | $\\sqrt{2}$ | | ₿ | 盒 360T.ALIAS.Company 3 | <b>Currency Group</b> | | | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | | 盒 360T.ALIAS.Company 4 | Default Group | | a <sub>1</sub> | | 360T ALIAS | Currency Added | $\\sqrt{2}$ | | <b>tip</b> | 宜 360T.ALIAS.Company 5 | | | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | | 盒 360T.ALIAS.Company 6 | | | | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | 同 | | | | | | 360T.ALIAS | Currency Added | $\\sqrt{2}$ | | | | | | | √ | 360T.ALIAS | Currency Removed | | | $\\vec{\\Sigma}$ | | | | | | | | | | ಥೆ | | | | | | | | | | | | | | | | | | | | | | | | <b>DX</b><br>$ 1^{2}$ | | | | | | | | Available Currencies | Select Member for \"Default Group\" | <b>Selected Currencies</b> | | | | | | | | | AUD | | | | | | | | | SEK | $\\geq$<br>CAD | | | | | | | | | SGD<br>ZAR | $\\overline{\\epsilon}$<br>CHF | | | | | | | | | AED | EUR | | | | | | | | | AFN | GBP | | | | | | | | | ALL | $\\gg$<br><b>USD</b> | | | | | | | | | AMD | $\\ll$ | | | | | | | | | ANG | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Figure 5 Bank Basket: Configuration: Live Audit Log ## 3 CONFIGURATION OF BANK BASKETS Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs: - Configuration Groups: Facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS; Order; SEP). Allow users to create a group one single time and reuse it across various rules. - RFS Bank Baskets: Provider Groups and temporarily Blocked Providers ma", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The toggle option allows the user to display only institutions in the navigation panel.", "Entity": ["toggle option", "user", "institutions", "navigation panel", "configuration tools"]}, {"Event": "The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.", "Entity": ["navigation panel", "minimize icon", "panel"]}, {"Event": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "Entity": ["entity tab", "Live Audit Log", "unsaved changes"]}, {"Event": "Individual unsaved changes can be reverted by clicking on the arrow icon.", "Entity": ["unsaved changes", "arrow icon"]}, {"Event": "Clicking on the \"Discard all changes\" button will revert all unsaved changes.", "Entity": ["\"Discard all changes\" button", "unsaved changes"]}, {"Event": "The image contains the text 'User Guide 360T Bank Baskets Configuration'.", "Entity": ["image", "text 'User Guide 360T Bank Baskets Configuration'"]}, {"Event": "Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.", "Entity": ["entity", "institution tree", "Configuration Groups tab", "data tabs"]}, {"Event": "Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.", "Entity": ["Configuration Groups", "parameters", "Bank Basket configuration", "RFS", "Order", "SEP"]}, {"Event": "Configuration Groups allow users to create a group one single time and reuse it across various rules.", "Entity": ["Configuration Groups", "users", "group", "rules"]}], "event_relation_dict": [{"Head": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "Relation": "as a result", "Tail": "Individual unsaved changes can be reverted by clicking on the arrow icon."}, {"Head": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "Relation": "as a result", "Tail": "Clicking on the \"Discard all changes\" button will revert all unsaved changes."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The toggle option allows the user to display only institutions in the navigation panel.\", \"Entity\": [\"toggle option\", \"user\", \"institutions\", \"navigation panel\", \"configuration tools\"]}, {\"Event\": \"The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.\", \"Entity\": [\"navigation panel\", \"minimize icon\", \"panel\"]}, {\"Event\": \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\", \"Entity\": [\"entity tab\", \"Live Audit Log\", \"unsaved changes\"]}, {\"Event\": \"Individual unsaved changes can be reverted by clicking on the arrow icon.\", \"Entity\": [\"unsaved changes\", \"arrow icon\"]}, {\"Event\": \"Clicking on the \\\"Discard all changes\\\" button will revert all unsaved changes.\", \"Entity\": [\"\\\"Discard all changes\\\" button\", \"unsaved changes\"]}, {\"Event\": \"The image contains the text 'User Guide 360T Bank Baskets Configuration'.\", \"Entity\": [\"image\", \"text 'User Guide 360T Bank Baskets Configuration'\"]}, {\"Event\": \"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.\", \"Entity\": [\"entity\", \"institution tree\", \"Configuration Groups tab\", \"data tabs\"]}, {\"Event\": \"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.\", \"Entity\": [\"Configuration Groups\", \"parameters\", \"Bank Basket configuration\", \"RFS\", \"Order\", \"SEP\"]}, {\"Event\": \"Configuration Groups allow users to create a group one single time and reuse it across various rules.\", \"Entity\": [\"Configuration Groups\", \"users\", \"group\", \"rules\"]}]", "output_stage_three": "[{\"Head\": \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\", \"Relation\": \"as a result\", \"Tail\": \"Individual unsaved changes can be reverted by clicking on the arrow icon.\"}, {\"Head\": \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\", \"Relation\": \"as a result\", \"Tail\": \"Clicking on the \\\"Discard all changes\\\" button will revert all unsaved changes.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 231, "time": 1.0}, "usage_stage_three": {"completion_tokens": 70, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "y be configured specifically for RFS requests. Bank Basket rules for four separate request types are configured on this tab: - o RFS FX Bank Baskets - o RFS MM Bank Baskets - o RFS Commodity Bank Baskets - o RFS Cross Currency Netting Bank Baskets - Order Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Orders. Bank Basket rules for Forward or Spot orders are configured on this tab. - SEP Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP). Bank Basket rules for SEP streaming spot executions are configured on this tab. Image /page/7/Picture/0 description: { \"image\\_description\": \"The image shows the logo of DEUTSCHE GROUP. The logo consists of two parts: a green graphic element on the left and the text \\\"DEUTSCHE GROUP\\\" on the right. The graphic element is a stylized representation of the letters \\\"360\\\" and a \\\"T\\\" inside a rounded rectangle. The text \\\"DEUTSCHE GROUP\\\" is in a sans-serif font, with \\\"DEUTSCHE\\\" stacked above \\\"GROUP\\\".\" } | RFS Requester | DEAL TRACKING | Bridge Administration | + | Preferences | Administration | Help | AA | X | |---------------|---------------|-----------------------|---|-------------|----------------|------|----|---| |---------------|---------------|-----------------------|---|-------------|----------------|------|----|---| | | | <input type=\"text\"/> | |--|--|----------------------| |--|--|----------------------| | | 360T.ALIAS | |--|----------------------| | | 360T.ALIAS.Company 1 | | | 360T.ALIAS Company 2 | | | 360T.ALIAS.Company 3 | | | 360T.ALIAS.Company 4 | | | 360T.ALIAS.Company 5 | | | 360T.ALIAS.Company 6 | | Configuration Groups | RFS Bank Baskets | Orders Bank Baskets | SEP Bank Baskets | | |----------------------|------------------------|-----------------------|-----------------------|----------------| | Currency Groups | Currency Couple Groups | FX Time Period Groups | MM Time Period Groups | Product Groups | | Currency Group | |----------------| | Default Group | | | | Create Group | |--|--|--------------| |--|--|--------------| | Create Change Request | Discard All Changes | Save | |-----------------------|---------------------|------| |-----------------------|---------------------|------| | 360T.ALIAS Company 1 X | |------------------------| |------------------------| | 360TAS Treasurer 1, 360T ALIAS // INT | Tue, 19. Jun 2018, 15:59:16 GMT // Connected [FFM] | |---------------------------------------|----------------------------------------------------| |---------------------------------------|----------------------------------------------------| Figure 6 Bank Basket: Configuration Data Tabs Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another i.e., an RFS configuration will have no impact on SEP trading, and vice v", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "RFS requests can be configured specifically.", "Entity": ["RFS requests"]}, {"Event": "Bank Basket rules for four separate request types are configured on this tab.", "Entity": ["Bank Basket rules", "RFS FX Bank Baskets", "RFS MM Bank Baskets", "RFS Commodity Bank Baskets", "RFS Cross Currency Netting Bank Baskets", "this tab"]}, {"Event": "Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.", "Entity": ["Provider Groups", "temporarily Blocked Providers", "Orders"]}, {"Event": "Bank Basket rules for Forward or Spot orders are configured on this tab.", "Entity": ["Bank Basket rules", "Forward orders", "Spot orders", "this tab"]}, {"Event": "Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).", "Entity": ["Provider Groups", "temporarily Blocked Providers", "Supersonic (SEP)"]}, {"Event": "Bank Basket rules for SEP streaming spot executions are configured on this tab.", "Entity": ["Bank Basket rules", "SEP streaming spot executions", "this tab"]}, {"Event": "The image shows the logo of DEUTSCHE GROUP.", "Entity": ["image", "logo", "DEUTSCHE GROUP"]}], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"RFS requests can be configured specifically.\", \"Entity\": [\"RFS requests\"]}, {\"Event\": \"Bank Basket rules for four separate request types are configured on this tab.\", \"Entity\": [\"Bank Basket rules\", \"RFS FX Bank Baskets\", \"RFS MM Bank Baskets\", \"RFS Commodity Bank Baskets\", \"RFS Cross Currency Netting Bank Baskets\", \"this tab\"]}, {\"Event\": \"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.\", \"Entity\": [\"Provider Groups\", \"temporarily Blocked Providers\", \"Orders\"]}, {\"Event\": \"Bank Basket rules for Forward or Spot orders are configured on this tab.\", \"Entity\": [\"Bank Basket rules\", \"Forward orders\", \"Spot orders\", \"this tab\"]}, {\"Event\": \"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).\", \"Entity\": [\"Provider Groups\", \"temporarily Blocked Providers\", \"Supersonic (SEP)\"]}, {\"Event\": \"Bank Basket rules for SEP streaming spot executions are configured on this tab.\", \"Entity\": [\"Bank Basket rules\", \"SEP streaming spot executions\", \"this tab\"]}, {\"Event\": \"The image shows the logo of DEUTSCHE GROUP.\", \"Entity\": [\"image\", \"logo\", \"DEUTSCHE GROUP\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 177, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "ersa. ### 3.1 Setting up Configuration Groups Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration. Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP. The groups themselves can be edited (values added or removed) without changing a set of rules based on those groups. The available parameters are: - o Currency Groups - o Currency Couple Groups - o FX Time Period Groups - o MM Time Period Groups A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes. - Create Group : To add a new group. - Rename Group : To change the name of a group. Cannot be used on the Default Group. - Remove Group : To delete an individual group. Cannot be used on the Default Group. If the removed group is used in any configured rules this group is replaced by the Default Group. - Save : Please remember to save changes to your configurations. Configuration Groups are particularly useful when configuring complex bank basket rules. - Note: It is not required to configure groups based on the above parameters. Users may still set individual custom rules without utilizing the Configuration Groups. Individual custom rules may be preferable for some users with less complex bank basket setups. - Note: Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group. The Default Group will include all existing values. All Default Groups can be modified (values may be removed). In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group. For example, if a new currency is added to the 360T platform the Default Currency Group will not include the new currency. The new currency must be selected by the user. #### 3.1.1 Defining Currency Groups Currency Groups are intended to allow the classification of single currencies into customized groups. This allows setting one single rule for each group of currencies rather than many rules for individual currencies. Currencies can be added or removed from the group without editing the rules themselves. Image /page/8/Picture/11 description: { \"image\\_description\": \"The image shows a user interface for managing currency groups in a financial application. The interface includes a list of available currencies and a list of selected currencies, allowing users to add or remove currencies from a group. The user is currently creating a currency group named G10.\" } Figure 7 Bank Basket: Currency Groups Once created, a currency group can be used to simplify rule creation for (1) interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and ", "entity_relation_dict": [{"Head": "Configuration Groups", "Relation": "facilitate", "Tail": "centralized management"}, {"Head": "parameters", "Relation": "used in", "Tail": "Bank Basket configuration"}, {"Head": "Groups", "Relation": "configured in", "Tail": "each parameter"}, {"Head": "Groups", "Relation": "reused for", "Tail": "creating rules"}, {"Head": "groups", "Relation": "edited without changing", "Tail": "set of rules"}, {"Head": "available parameters", "Relation": "include", "Tail": "Currency Groups"}, {"Head": "available parameters", "Relation": "include", "Tail": "Currency Couple Groups"}, {"Head": "available parameters", "Relation": "include", "Tail": "FX Time Period Groups"}, {"Head": "available parameters", "Relation": "include", "Tail": "MM Time Period Groups"}, {"Head": "icons", "Relation": "allow user to", "Tail": "create new group"}, {"Head": "icons", "Relation": "allow user to", "Tail": "edit name of existing group"}, {"Head": "icons", "Relation": "allow user to", "Tail": "delete group"}, {"Head": "icons", "Relation": "allow user to", "Tail": "save changes"}, {"Head": "Rename Group", "Relation": "cannot be used on", "Tail": "Default Group"}, {"Head": "Remove Group", "Relation": "cannot be used on", "Tail": "Default Group"}, {"Head": "removed group", "Relation": "replaced by", "Tail": "Default Group"}, {"Head": "Configuration Groups", "Relation": "useful for", "Tail": "configuring complex bank basket rules"}, {"Head": "Users", "Relation": "set", "Tail": "individual custom rules"}, {"Head": "Individual custom rules", "Relation": "preferable for", "Tail": "users with less complex bank basket setups"}, {"Head": "Bank Basket Configuration", "Relation": "contains", "Tail": "Default Group"}, {"Head": "Default Group", "Relation": "includes", "Tail": "existing values"}, {"Head": "Default Groups", "Relation": "modified by removing", "Tail": "values"}, {"Head": "new values", "Relation": "not added to", "Tail": "Default Group"}, {"Head": "new currency", "Relation": "not included in", "Tail": "Default Currency Group"}, {"Head": "new currency", "Relation": "selected by", "Tail": "user"}, {"Head": "Currency Groups", "Relation": "allow classification of", "Tail": "single currencies"}, {"Head": "Currency Groups", "Relation": "allow setting", "Tail": "single rule for group of currencies"}, {"Head": "Currencies", "Relation": "added or removed from", "Tail": "group"}, {"Head": "currency group", "Relation": "simplifies", "Tail": "rule creation for interest rate products"}, {"Head": "interest rate products", "Relation": "include", "Tail": "Loan"}, {"Head": "interest rate products", "Relation": "include", "Tail": "Deposit"}, {"Head": "interest rate products", "Relation": "include", "Tail": "Interest Rate Swap"}, {"Head": "interest rate products", "Relation": "include", "Tail": "FRA"}, {"Head": "interest rate products", "Relation": "include", "Tail": "CapFloor"}], "event_entity_relation_dict": [{"Event": "Configuration Groups facilitate centralized management of parameters.", "Entity": ["Configuration Groups", "parameters", "Bank Basket configuration"]}, {"Event": "Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.", "Entity": ["Groups", "parameter", "rules", "requests", "RFS", "Orders", "SEP"]}, {"Event": "The groups themselves can be edited without changing a set of rules based on those groups.", "Entity": ["groups", "values", "rules"]}, {"Event": "The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.", "Entity": ["parameters", "Currency Groups", "Currency Couple Groups", "FX Time Period Groups", "MM Time Period Groups"]}, {"Event": "A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.", "Entity": ["icons", "Configuration Group area", "user", "group", "name", "changes"]}, {"Event": "Create Group adds a new group.", "Entity": ["Create Group", "group"]}, {"Event": "Rename Group changes the name of a group.", "Entity": ["Rename Group", "name", "group"]}, {"Event": "Rename Group cannot be used on the Default Group.", "Entity": ["Rename Group", "Default Group"]}, {"Event": "Remove Group deletes an individual group.", "Entity": ["Remove Group", "group"]}, {"Event": "Remove Group cannot be used on the Default Group.", "Entity": ["Remove Group", "Default Group"]}, {"Event": "If the removed group is used in any configured rules this group is replaced by the Default Group.", "Entity": ["removed group", "rules", "Default Group"]}, {"Event": "Users are reminded to save changes to their configurations.", "Entity": ["users", "changes", "configurations"]}, {"Event": "Configuration Groups are particularly useful when configuring complex bank basket rules.", "Entity": ["Configuration Groups", "bank basket rules"]}], "event_relation_dict": [], "output_stage_one": "[{\"Head\": \"Configuration Groups\", \"Relation\": \"facilitate\", \"Tail\": \"centralized management\"}, {\"Head\": \"parameters\", \"Relation\": \"used in\", \"Tail\": \"Bank Basket configuration\"}, {\"Head\": \"Groups\", \"Relation\": \"configured in\", \"Tail\": \"each parameter\"}, {\"Head\": \"Groups\", \"Relation\": \"reused for\", \"Tail\": \"creating rules\"}, {\"Head\": \"groups\", \"Relation\": \"edited without changing\", \"Tail\": \"set of rules\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"Currency Groups\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"Currency Couple Groups\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"FX Time Period Groups\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"MM Time Period Groups\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"create new group\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"edit name of existing group\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"delete group\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"save changes\"}, {\"Head\": \"Rename Group\", \"Relation\": \"cannot be used on\", \"Tail\": \"Default Group\"}, {\"Head\": \"Remove Group\", \"Relation\": \"cannot be used on\", \"Tail\": \"Default Group\"}, {\"Head\": \"removed group\", \"Relation\": \"replaced by\", \"Tail\": \"Default Group\"}, {\"Head\": \"Configuration Groups\", \"Relation\": \"useful for\", \"Tail\": \"configuring complex bank basket rules\"}, {\"Head\": \"Users\", \"Relation\": \"set\", \"Tail\": \"individual custom rules\"}, {\"Head\": \"Individual custom rules\", \"Relation\": \"preferable for\", \"Tail\": \"users with less complex bank basket setups\"}, {\"Head\": \"Bank Basket Configuration\", \"Relation\": \"contains\", \"Tail\": \"Default Group\"}, {\"Head\": \"Default Group\", \"Relation\": \"includes\", \"Tail\": \"existing values\"}, {\"Head\": \"Default Groups\", \"Relation\": \"modified by removing\", \"Tail\": \"values\"}, {\"Head\": \"new values\", \"Relation\": \"not added to\", \"Tail\": \"Default Group\"}, {\"Head\": \"new currency\", \"Relation\": \"not included in\", \"Tail\": \"Default Currency Group\"}, {\"Head\": \"new currency\", \"Relation\": \"selected by\", \"Tail\": \"user\"}, {\"Head\": \"Currency Groups\", \"Relation\": \"allow classification of\", \"Tail\": \"single currencies\"}, {\"Head\": \"Currency Groups\", \"Relation\": \"allow setting\", \"Tail\": \"single rule for group of currencies\"}, {\"Head\": \"Currencies\", \"Relation\": \"added or removed from\", \"Tail\": \"group\"}, {\"Head\": \"currency group\", \"Relation\": \"simplifies\", \"Tail\": \"rule creation for interest rate products\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"Loan\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"Deposit\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"Interest Rate Swap\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"FRA\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"CapFloor\"}]", "output_stage_two": "[{\"Event\": \"Configuration Groups facilitate centralized management of parameters.\", \"Entity\": [\"Configuration Groups\", \"parameters\", \"Bank Basket configuration\"]}, {\"Event\": \"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.\", \"Entity\": [\"Groups\", \"parameter\", \"rules\", \"requests\", \"RFS\", \"Orders\", \"SEP\"]}, {\"Event\": \"The groups themselves can be edited without changing a set of rules based on those groups.\", \"Entity\": [\"groups\", \"values\", \"rules\"]}, {\"Event\": \"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.\", \"Entity\": [\"parameters\", \"Currency Groups\", \"Currency Couple Groups\", \"FX Time Period Groups\", \"MM Time Period Groups\"]}, {\"Event\": \"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.\", \"Entity\": [\"icons\", \"Configuration Group area\", \"user\", \"group\", \"name\", \"changes\"]}, {\"Event\": \"Create Group adds a new group.\", \"Entity\": [\"Create Group\", \"group\"]}, {\"Event\": \"Rename Group changes the name of a group.\", \"Entity\": [\"Rename Group\", \"name\", \"group\"]}, {\"Event\": \"Rename Group cannot be used on the Default Group.\", \"Entity\": [\"Rename Group\", \"Default Group\"]}, {\"Event\": \"Remove Group deletes an individual group.\", \"Entity\": [\"Remove Group\", \"group\"]}, {\"Event\": \"Remove Group cannot be used on the Default Group.\", \"Entity\": [\"Remove Group\", \"Default Group\"]}, {\"Event\": \"If the removed group is used in any configured rules this group is replaced by the Default Group.\", \"Entity\": [\"removed group\", \"rules\", \"Default Group\"]}, {\"Event\": \"Users are reminded to save changes to their configurations.\", \"Entity\": [\"users\", \"changes\", \"configurations\"]}, {\"Event\": \"Configuration Groups are particularly useful when configuring complex bank basket rules.\", \"Entity\": [\"Configuration Groups\", \"bank basket rules\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 399, "time": 1.0}, "usage_stage_two": {"completion_tokens": 293, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "Tri Party Repo found in the RFS MM Bank Baskets area and (2) Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area. Image /page/9/Picture/0 description: { \"image\\_description\": \"The image shows a green and white logo with the number 360 and a symbol that looks like a T.\" } User Guide 360T Bank Baskets Configuration | 侖 | Q ※ 上 二<br>$\\langle$ | | <b>Configuration Groups</b> | <b>RFS Bank Baskets</b> | Orders Bank Baskets | SEP Bank Baskets Copy To | $ \\mathcal{E}_\\lambda $ | | | | の位置 | |----------------------------------|------------------------------------------------------------|--------------|-----------------------------|------------------------------|-----------------------------------------|--------------------------|----------------------------|--------------|-----------------------------------------|--------------|----------| | $\\mathcal{G}$ | $\\wedge$ $\\hat{\\Xi}$ 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | | RFS Provider Groups | <b>Blocked RFS Providers</b> | RFS FX Bank Baskets RFS MM Bank Baskets | -- | RFS Commodity Bank Baskets | | RFS Cross Currency Netting Bank Baskets | | | | -<br>$\\Box$ | 盒 360T.ALIAS.Company 2 | | POS Product Type | | Currency | | <b>Time Period</b> | | <b>Bank Basket</b> | | | | 凾 | 盒 360T.ALIAS.Company 3<br><sup> 360T ALIAS Company 4</sup> | $\\mathbf{1}$ | Any | $\\vee$ | Any | $\\widehat{\\phantom{a}}$ | Any | $\\checkmark$ | Default Group | $\\checkmark$ | 図自 | | | 盒 360T ALIAS Company 5<br>章 360T ALIAS Company 6 | | | | Any<br>Default Group | | | | | | Add Rule | | $\\quad \\ \\ \\, \\textcircled{F}$ | | | | | 610. | | | | | | | | | | | | | Custom | | | | | | | | ಂದ್ರಿ | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Discard All Changes | <b>Save</b> | | Figure 8 Bank Basket: RFS MM Bank Baskets A default group exists which includes all currencies. This group cannot be removed or renamed. However, the currencies in the group can be altered as described below. | | | Preferences Administration Help AA - X | | |--|----------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|------| | | RFS REQUESTER DEAL TRACKING | + BRIDGE ADMINISTRATION | | | | Q | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To... | | | | 360T ALIAS | Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product Groups | | | | 360T.", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [{"Head": "Tri Party Repo found in the RFS MM Bank Baskets area.", "Relation": "at the same time", "Tail": "Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area."}, {"Head": "A default group exists which includes all currencies.", "Relation": "at the same time", "Tail": "This group cannot be removed or renamed."}, {"Head": "A default group exists which includes all currencies.", "Relation": "at the same time", "Tail": "The currencies in the group can be altered as described below."}, {"Head": "This group cannot be removed or renamed.", "Relation": "at the same time", "Tail": "The currencies in the group can be altered as described below."}], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[{\"Head\": \"Tri Party Repo found in the RFS MM Bank Baskets area.\", \"Relation\": \"at the same time\", \"Tail\": \"Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.\"}, {\"Head\": \"A default group exists which includes all currencies.\", \"Relation\": \"at the same time\", \"Tail\": \"This group cannot be removed or renamed.\"}, {\"Head\": \"A default group exists which includes all currencies.\", \"Relation\": \"at the same time\", \"Tail\": \"The currencies in the group can be altered as described below.\"}, {\"Head\": \"This group cannot be removed or renamed.\", \"Relation\": \"at the same time\", \"Tail\": \"The currencies in the group can be altered as described below.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 0, "time": 0}, "usage_stage_three": {"completion_tokens": 116, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "ALIAS Company 1<br>360T.ALIAS.Company 2<br>360T.ALIAS.Company 3<br>360T.ALIAS.Company 4<br>360T.ALIAS.Company 5<br>360T.ALIAS.Company 6 | Currency Group <div>Default Group</div> <div>G10</div> <div>Create Group</div> | | | | | | | | | | Select Member for \"Default Group\" | - X | | | | Available Currencies Selected Currencies <div>AUD</div> <div>CAD</div> <div>CHF</div> <div>EUR</div> <div>GBP</div> <div>HKD</div> <div>JPY</div> <div>NOK</div> | | | | | Discard All Changes Create Change Request 360T.ALIAS X | Save | | | 360TAS.Treasurer1, 360T ALIAS // INT | Fri, 06. Jul 2018, 07:39:04 GMT // Connected [FFM] | | Figure 9 Bank Basket: Currency Groups Default Group Currencies may be added or removed from the default group. A currency is highlighted with a single-click. This activates the single arrow. Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available. All currencies can be moved in either direction by using the double arrows. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Please, provide a name for a new group | | G10 | |--|-----| |--|-----| | Cancel | Create Group | |--------|--------------| |--------|--------------| Figure 10 Bank Basket: Currency Groups Create Group To view the currencies configured for the group, click on the Currency Group name. | Preferences | Administration | Help | AA | |-------------|----------------|------|----| |-------------|----------------|------|----| RFS REQUESTER DEAL TRACKING BRIDGE ADMINISTRATIONConfiguration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To...Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product GroupsCurrency Group Default Group G10 Create GroupSelect Member for \"G10\"Available Currencies HKD PLN SGD ZAR AED AFN ALLSelected Currencies AUD CAD CHF EUR GBP JPY NOK NZDCreate Change Request Discard All Changes SaveFigure 11 Bank Basket: Configured Currency Group Note: The same currency can be added to many different groups. The system does not restrict the creation of groups with overlapping sets of currencies. #### 3.1.2 Defining Currency Couple Groups Currency Couple Groups allow the creation of \"buckets\" of currency pairs. Once created, a Currency Couple Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades etc; (2) Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area; and also (3) Order Spot and Forwards found in the Orders Bank Basket area. | | | | | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\Diamond$ AA $\\Box$ $\\Box$ X | |----------------------------------------------------------------|--------------------------------------------------------------|--------------------", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "Currencies may be added or removed from the default group.", "Entity": ["Currencies", "default group"]}, {"Event": "A currency is highlighted with a single-click.", "Entity": ["A currency", "single-click"]}, {"Event": "Highlighting a currency activates the single arrow.", "Entity": ["Highlighting a currency", "single arrow"]}, {"Event": "Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.", "Entity": ["single arrow", "desired currency", "Available", "Selected"]}, {"Event": "All currencies can be moved in either direction by using the double arrows.", "Entity": ["All currencies", "double arrows"]}, {"Event": "A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.", "Entity": ["new group", "Create Group", "desired name", "Save"]}, {"Event": "Currencies configured for the group can be viewed by clicking on the Currency Group name.", "Entity": ["currencies", "group", "Currency Group name"]}, {"Event": "The same currency can be added to many different groups.", "Entity": ["currency", "groups"]}, {"Event": "The system does not restrict the creation of groups with overlapping sets of currencies.", "Entity": ["system", "creation of groups", "overlapping sets of currencies"]}, {"Event": "Currency Couple Groups allow the creation of \"buckets\" of currency pairs.", "Entity": ["Currency Couple Groups", "buckets of currency pairs"]}, {"Event": "A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.", "Entity": ["Currency Couple Group", "rules", "FX products", "RFS FX Bank Baskets area", "FX Spot", "Forwards", "Swaps", "NDF", "NDS", "Options", "Block Trades", "Energy Asian Swaps", "Bullet Swaps", "RFS Commodity Bank Baskets area", "Order Spot", "Forwards", "Orders Bank Basket area"]}], "event_relation_dict": [{"Head": "A currency is highlighted with a single-click.", "Relation": "as a result", "Tail": "The single arrow is activated."}, {"Head": "The single arrow is activated.", "Relation": "before", "Tail": "Clicking the single arrow moves the desired currency."}, {"Head": "A user clicks Create Group.", "Relation": "before", "Tail": "A user types the desired name."}, {"Head": "A user types the desired name.", "Relation": "before", "Tail": "A user clicks Create Group again."}, {"Head": "A user clicks Create Group again.", "Relation": "before", "Tail": "A user clicks Save."}, {"Head": "A user clicks on the Currency Group name.", "Relation": "as a result", "Tail": "The currencies configured for the group are viewed."}, {"Head": "A Currency Couple Group is created.", "Relation": "as a result", "Tail": "The Currency Couple Group can be used to simplify rules."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"Currencies may be added or removed from the default group.\", \"Entity\": [\"Currencies\", \"default group\"]}, {\"Event\": \"A currency is highlighted with a single-click.\", \"Entity\": [\"A currency\", \"single-click\"]}, {\"Event\": \"Highlighting a currency activates the single arrow.\", \"Entity\": [\"Highlighting a currency\", \"single arrow\"]}, {\"Event\": \"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.\", \"Entity\": [\"single arrow\", \"desired currency\", \"Available\", \"Selected\"]}, {\"Event\": \"All currencies can be moved in either direction by using the double arrows.\", \"Entity\": [\"All currencies\", \"double arrows\"]}, {\"Event\": \"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.\", \"Entity\": [\"new group\", \"Create Group\", \"desired name\", \"Save\"]}, {\"Event\": \"Currencies configured for the group can be viewed by clicking on the Currency Group name.\", \"Entity\": [\"currencies\", \"group\", \"Currency Group name\"]}, {\"Event\": \"The same currency can be added to many different groups.\", \"Entity\": [\"currency\", \"groups\"]}, {\"Event\": \"The system does not restrict the creation of groups with overlapping sets of currencies.\", \"Entity\": [\"system\", \"creation of groups\", \"overlapping sets of currencies\"]}, {\"Event\": \"Currency Couple Groups allow the creation of \\\"buckets\\\" of currency pairs.\", \"Entity\": [\"Currency Couple Groups\", \"buckets of currency pairs\"]}, {\"Event\": \"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.\", \"Entity\": [\"Currency Couple Group\", \"rules\", \"FX products\", \"RFS FX Bank Baskets area\", \"FX Spot\", \"Forwards\", \"Swaps\", \"NDF\", \"NDS\", \"Options\", \"Block Trades\", \"Energy Asian Swaps\", \"Bullet Swaps\", \"RFS Commodity Bank Baskets area\", \"Order Spot\", \"Forwards\", \"Orders Bank Basket area\"]}]", "output_stage_three": "[{\"Head\": \"A currency is highlighted with a single-click.\", \"Relation\": \"as a result\", \"Tail\": \"The single arrow is activated.\"}, {\"Head\": \"The single arrow is activated.\", \"Relation\": \"before\", \"Tail\": \"Clicking the single arrow moves the desired currency.\"}, {\"Head\": \"A user clicks Create Group.\", \"Relation\": \"before\", \"Tail\": \"A user types the desired name.\"}, {\"Head\": \"A user types the desired name.\", \"Relation\": \"before\", \"Tail\": \"A user clicks Create Group again.\"}, {\"Head\": \"A user clicks Create Group again.\", \"Relation\": \"before\", \"Tail\": \"A user clicks Save.\"}, {\"Head\": \"A user clicks on the Currency Group name.\", \"Relation\": \"as a result\", \"Tail\": \"The currencies configured for the group are viewed.\"}, {\"Head\": \"A Currency Couple Group is created.\", \"Relation\": \"as a result\", \"Tail\": \"The Currency Couple Group can be used to simplify rules.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 316, "time": 1.0}, "usage_stage_three": {"completion_tokens": 142, "time": 1.0}}
