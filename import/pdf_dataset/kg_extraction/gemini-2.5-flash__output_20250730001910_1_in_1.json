{"id": "1", "metadata": {"lang": "en"}, "original_text": "## BANK BASKET CONFIGURATION (BRIDGE ADMINISTRATION) Image /page/0/Picture/1 description: { \"image\\_description\": \"The image shows a logo for \\\"360T\\\", which is a green rectangle with rounded corners. The text \\\"360\\\" is in white with a green outline, and the \\\"T\\\" is also in white with a green outline. There are two arrows pointing towards each other in the middle of the \\\"0\\\" in \\\"360\\\".\" } # TEX MULTIDEALER TRADING SYSTEM USER GUIDE 360T BRIDGE ADMINISTRATION: ENHANCED BANK BASKET CONFIGURATION © 360 TREASURY SYSTEMS AG, 2019 THIS FILE CONTAINS PROPRIETARY AND CONFIDENTIAL INFORMATION INCLUDING TRADE SECRETS AND MAY NOT BE DIVULGED TO ANY THIRD PARTY WITHOUT PRIOR WRITTEN APPROVAL FROM 360 TREASURY SYSTEMS AG ## CONTENTS | 1 | | INTRODUCTION | 4 | |---|-------|---------------------------------------|----| | 2 | | GETTING STARTED | 4 | | 3 | | CONFIGURATION OF BANK BASKETS | 7 | | | 3.1 | SETTING UP CONFIGURATION GROUPS | 8 | | | 3.1.1 | Defining Currency Groups | 9 | | | 3.1.2 | Defining Currency Couple Groups | 11 | | | 3.1.3 | Defining FX Time Period Groups | 13 | | | 3.1.4 | Defining MM Time Period Groups | 14 | | | 3.1.5 | Defining Product Groups | 15 | | | 3.2 | PROVIDER GROUPS AND BLOCKED PROVIDERS | 16 | | | 3.3 | BANK BASKET RULES | 18 | | | 3.3.1 | Defining Bank Basket Rules | 18 | | | 3.3.2 | Order of the Rules | 19 | | | 3.4 | NOTE ON SUPERSONIC | 19 | | 4 | | BANK BASKETS EVALUATOR TOOL | 19 | | 5 | | CONTACT 360T | 22 | ## TABLE OF FIGURES | Figure 1 Header Bar | 4 | |-----------------------------------------------------------------|----| | Figure 2 Bridge Administration: Homepage | 5 | | Figure 3 Bank Basket: Start page | 5 | | Figure 4 Bank Basket: Configuration for a selected legal entity | 6 | | Figure 5 Bank Basket: Configuration: Live Audit Log | 7 | | Figure 6 Bank Basket: Configuration Data Tabs | 8 | | Figure 7 Bank Basket: Currency Groups | 9 | | Figure 8 Bank Basket: RFS MM Bank Baskets | 10 | | Figure 9 Bank Basket: Currency Groups Default Group | 10 | | Figure 10 Bank Basket: Currency Groups Create Group | 11 | | Figure 11 Bank Basket: Configured Currency Group | 11 | | Figure 12 Bank Basket: Currency Couple Groups | 12 | | Figure 13 Bank Basket: Add Currency Couple | 13 | | Figure 14 Bank Basket: Add FX Time Period | 14 | | Figure 15 Bank Basket: Add MM Time Period | 15 | | Figure 16 Bank Basket: Product Groups | 16 | | Figure 17 Bank Basket: Product Groups Create Group | 16 | | Figure 18 Bank Basket: Blocked RFS Providers | 17 | | Figure 19 Bank Basket: RFS Provider Groups | 17 | | Figure 20 Bank Basket: Add Rule 18 | | |---------------------------------------------------------|--| | Figure 21 Use Custom selection for Currency couples 19 | | | Figure 22 Bank Basket: Order of Rules 19 | | | Figure 23 Evaluator Tool Quick Link 20 | | | Figure 24 Evaluator Tool icon 20 | | | Figure 24 Evaluator Tool 21 | ", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The image shows a logo for 360T.", "Entity": ["image", "logo", "360T"]}, {"Event": "The logo is a green rectangle with rounded corners.", "Entity": ["logo", "green rectangle", "rounded corners"]}, {"Event": "The text 360 is in white with a green outline.", "Entity": ["text", "360", "white", "green outline"]}, {"Event": "The T is in white with a green outline.", "Entity": ["T", "white", "green outline"]}, {"Event": "Two arrows point towards each other in the middle of the 0 in 360.", "Entity": ["arrows", "0", "360"]}, {"Event": "This file contains proprietary and confidential information including trade secrets.", "Entity": ["file", "proprietary information", "confidential information", "trade secrets"]}, {"Event": "The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.", "Entity": ["information", "third party", "written approval", "360 TREASURY SYSTEMS AG"]}, {"Event": "360 TREASURY SYSTEMS AG holds the copyright for 2019.", "Entity": ["360 TREASURY SYSTEMS AG", "copyright", "2019"]}], "event_relation_dict": [{"Head": "The introduction is presented.", "Relation": "before", "Tail": "Getting started information is provided."}, {"Head": "Getting started information is provided.", "Relation": "before", "Tail": "Bank baskets are configured."}, {"Head": "Bank baskets are configured.", "Relation": "before", "Tail": "The bank baskets evaluator tool is used."}, {"Head": "The bank baskets evaluator tool is used.", "Relation": "before", "Tail": "Contact information for 360T is available."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The image shows a logo for 360T.\", \"Entity\": [\"image\", \"logo\", \"360T\"]}, {\"Event\": \"The logo is a green rectangle with rounded corners.\", \"Entity\": [\"logo\", \"green rectangle\", \"rounded corners\"]}, {\"Event\": \"The text 360 is in white with a green outline.\", \"Entity\": [\"text\", \"360\", \"white\", \"green outline\"]}, {\"Event\": \"The T is in white with a green outline.\", \"Entity\": [\"T\", \"white\", \"green outline\"]}, {\"Event\": \"Two arrows point towards each other in the middle of the 0 in 360.\", \"Entity\": [\"arrows\", \"0\", \"360\"]}, {\"Event\": \"This file contains proprietary and confidential information including trade secrets.\", \"Entity\": [\"file\", \"proprietary information\", \"confidential information\", \"trade secrets\"]}, {\"Event\": \"The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.\", \"Entity\": [\"information\", \"third party\", \"written approval\", \"360 TREASURY SYSTEMS AG\"]}, {\"Event\": \"360 TREASURY SYSTEMS AG holds the copyright for 2019.\", \"Entity\": [\"360 TREASURY SYSTEMS AG\", \"copyright\", \"2019\"]}]", "output_stage_three": "[{\"Head\": \"The introduction is presented.\", \"Relation\": \"before\", \"Tail\": \"Getting started information is provided.\"}, {\"Head\": \"Getting started information is provided.\", \"Relation\": \"before\", \"Tail\": \"Bank baskets are configured.\"}, {\"Head\": \"Bank baskets are configured.\", \"Relation\": \"before\", \"Tail\": \"The bank baskets evaluator tool is used.\"}, {\"Head\": \"The bank baskets evaluator tool is used.\", \"Relation\": \"before\", \"Tail\": \"Contact information for 360T is available.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 165, "time": 1.0}, "usage_stage_three": {"completion_tokens": 68, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "| | Figure 26 Evaluator Tool: Highlighted Rule 21 | | ## 1 INTRODUCTION This user manual describes the Bank Basket feature of the 360T Bridge Administration tool. The Bank Basket feature has been enhanced to provide improved rule management capabilities, including the introduction of configuration groups based on currency, currency couple, time period and product(s); configuration of separate baskets by request type (RFS, Order, SEP); as well as the ability to apply and remove temporary blocks without affecting the configured rules or counterpart relationship(s). #### Please note: The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications. Only users with corresponding user rights are able to administer the company's Bank Baskets. <NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights. ## 2 GETTING STARTED The Bank Basket configuration is found within the Bridge Administration tool. Bridge Administration can be accessed via the menu option \"Administration\" in the screen header of the Bridge application. | | | | $\\vee$ Preferences<br>V Administration | $\\times$ Help<br>$\\Box$<br>A <sub>A</sub><br>$\\mathbb{X}$ | |-----------------------------------------------------------------------------|-------------------------------------------------------------------------------------|----------------------------------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------------------------| | > Bridge Administration | | | | X | | <b>SCIENCES</b> | | | <b>SCIENS</b> | | | <b>UNIT SIDEN</b><br>$^{117}890$<br><sup>⊞7</sup> 92α<br>Spot // 21.11.2017 | <b><i>STATISTICS</i></b><br><b>POST MARK</b><br>1327<br>##336<br>Spot // 21 11.2017 | <b>STATISTICS</b><br><b>START CORPORA</b><br>084<br>0.55<br>5001//21112017 | <b>Life of Life and</b><br>889184<br>0.994<br>93.<br>Spot // 2111 2017 | | | 11703<br><b>ILFOG.</b> | 132.2 d | 08900 mm<br><b>TIDO AT TELL</b> | 0.99 ft J Lune<br>0994 T | | Figure 1 Header Bar The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user. A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage. Image /page/4/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user guide. The screen displays the 'Administration Start' page with options for 'Regulatory Data', 'Bank Baskets', 'Change Request', 'Wizards', and 'Evaluator Tools'. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with preferences and help options. The bottom of the screen display", "entity_relation_dict": [{"Head": "user manual", "Relation": "describes", "Tail": "Bank Basket feature"}, {"Head": "Bank Basket feature", "Relation": "is part of", "Tail": "360T Bridge Administration tool"}, {"Head": "Bank Basket feature", "Relation": "enhanced to provide", "Tail": "improved rule management capabilities"}, {"Head": "improved rule management capabilities", "Relation": "include", "Tail": "configuration groups"}, {"Head": "configuration groups", "Relation": "based on", "Tail": "currency"}, {"Head": "configuration groups", "Relation": "based on", "Tail": "currency couple"}, {"Head": "configuration groups", "Relation": "based on", "Tail": "time period"}, {"Head": "configuration groups", "Relation": "based on", "Tail": "product(s)"}, {"Head": "improved rule management capabilities", "Relation": "include", "Tail": "configuration of separate baskets"}, {"Head": "separate baskets", "Relation": "configured by", "Tail": "request type (RFS, Order, SEP)"}, {"Head": "improved rule management capabilities", "Relation": "include", "Tail": "ability to apply and remove temporary blocks"}, {"Head": "temporary blocks", "Relation": "do not affect", "Tail": "configured rules"}, {"Head": "temporary blocks", "Relation": "do not affect", "Tail": "counterpart relationship(s)"}, {"Head": "360T enhanced Bank Basket feature", "Relation": "available to", "Tail": "entities"}, {"Head": "entities", "Relation": "have", "Tail": "EMS application"}, {"Head": "entities", "Relation": "have", "Tail": "Bridge application"}, {"Head": "users", "Relation": "have", "Tail": "corresponding user rights"}, {"Head": "users", "Relation": "administer", "Tail": "company's Bank Baskets"}, {"Head": "<EMAIL>", "Relation": "can set up", "Tail": "relevant administrative rights"}, {"Head": "customer relationship manager", "Relation": "can set up", "Tail": "relevant administrative rights"}, {"Head": "Bank Basket configuration", "Relation": "found within", "Tail": "Bridge Administration tool"}, {"Head": "Bridge Administration", "Relation": "accessed via", "Tail": "menu option \"Administration\""}, {"Head": "menu option \"Administration\"", "Relation": "located in", "Tail": "screen header"}, {"Head": "screen header", "Relation": "is part of", "Tail": "Bridge application"}, {"Head": "Bridge Administration feature", "Relation": "opens to", "Tail": "homepage"}, {"Head": "homepage", "Relation": "has", "Tail": "available shortcuts"}, {"Head": "available shortcuts", "Relation": "lead to", "Tail": "different configuration tools"}, {"Head": "different configuration tools", "Relation": "are for", "Tail": "particular user"}, {"Head": "quick navigation toolbar", "Relation": "shows", "Tail": "active homepage icon"}, {"Head": "quick navigation toolbar", "Relation": "available on", "Tail": "left side of the homepage"}, {"Head": "image", "Relation": "shows", "Tail": "screenshot"}, {"Head": "screenshot", "Relation": "is of", "Tail": "360T Bank Baskets Configuration user guide"}, {"Head": "screen", "Relation": "displays", "Tail": "'Administration Start' page"}, {"Head": "'Administration Start' page", "Relation": "has option for", "Tail": "'Regulatory Data'"}, {"Head": "'Administration Start' page", "Relation": "has option for", "Tail": "'Bank Baskets'"}, {"Head": "'Administration Start' page", "Relation": "has option for", "Tail": "'Change Request'"}, {"Head": "'Administration Start' page", "Relation": "has option for", "Tail": "'Wizards'"}, {"Head": "'Administration Start' page", "Relation": "has option for", "Tail": "'Evaluator Tools'"}, {"Head": "top of the screen", "Relation": "includes", "Tail": "tabs"}, {"Head": "tabs", "Relation": "include", "Tail": "'RFS REQUESTER'"}, {"Head": "tabs", "Relation": "include", "Tail": "'DEAL TRACKING'"}, {"Head": "tabs", "Relation": "include", "Tail": "'BRIDGE ADMINISTRATION'"}, {"Head": "top of the screen", "Relation": "includes", "Tail": "preferences options"}, {"Head": "top of the screen", "Relation": "includes", "Tail": "help options"}], "event_entity_relation_dict": [{"Event": "This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.", "Entity": ["user manual", "Bank Basket feature", "360T Bridge Administration tool"]}, {"Event": "The Bank Basket feature has been enhanced.", "Entity": ["Bank Basket feature"]}, {"Event": "The enhancement provides improved rule management capabilities.", "Entity": ["enhancement", "rule management capabilities"]}, {"Event": "Configuration groups based on currency, currency couple, time period and product(s) have been introduced.", "Entity": ["configuration groups", "currency", "currency couple", "time period", "product(s)"]}, {"Event": "Separate baskets can be configured by request type.", "Entity": ["separate baskets", "request type"]}, {"Event": "Request types include RFS.", "Entity": ["RFS", "request types"]}, {"Event": "Request types include Order.", "Entity": ["Order", "request types"]}, {"Event": "Request types include SEP.", "Entity": ["SEP", "request types"]}, {"Event": "Temporary blocks can be applied.", "Entity": ["temporary blocks"]}, {"Event": "Temporary blocks can be removed.", "Entity": ["temporary blocks"]}, {"Event": "Applying temporary blocks does not affect the configured rules.", "Entity": ["temporary blocks", "configured rules"]}], "event_relation_dict": [], "output_stage_one": "[{\"Head\": \"user manual\", \"Relation\": \"describes\", \"Tail\": \"Bank Basket feature\"}, {\"Head\": \"Bank Basket feature\", \"Relation\": \"is part of\", \"Tail\": \"360T Bridge Administration tool\"}, {\"Head\": \"Bank Basket feature\", \"Relation\": \"enhanced to provide\", \"Tail\": \"improved rule management capabilities\"}, {\"Head\": \"improved rule management capabilities\", \"Relation\": \"include\", \"Tail\": \"configuration groups\"}, {\"Head\": \"configuration groups\", \"Relation\": \"based on\", \"Tail\": \"currency\"}, {\"Head\": \"configuration groups\", \"Relation\": \"based on\", \"Tail\": \"currency couple\"}, {\"Head\": \"configuration groups\", \"Relation\": \"based on\", \"Tail\": \"time period\"}, {\"Head\": \"configuration groups\", \"Relation\": \"based on\", \"Tail\": \"product(s)\"}, {\"Head\": \"improved rule management capabilities\", \"Relation\": \"include\", \"Tail\": \"configuration of separate baskets\"}, {\"Head\": \"separate baskets\", \"Relation\": \"configured by\", \"Tail\": \"request type (RFS, Order, SEP)\"}, {\"Head\": \"improved rule management capabilities\", \"Relation\": \"include\", \"Tail\": \"ability to apply and remove temporary blocks\"}, {\"Head\": \"temporary blocks\", \"Relation\": \"do not affect\", \"Tail\": \"configured rules\"}, {\"Head\": \"temporary blocks\", \"Relation\": \"do not affect\", \"Tail\": \"counterpart relationship(s)\"}, {\"Head\": \"360T enhanced Bank Basket feature\", \"Relation\": \"available to\", \"Tail\": \"entities\"}, {\"Head\": \"entities\", \"Relation\": \"have\", \"Tail\": \"EMS application\"}, {\"Head\": \"entities\", \"Relation\": \"have\", \"Tail\": \"Bridge application\"}, {\"Head\": \"users\", \"Relation\": \"have\", \"Tail\": \"corresponding user rights\"}, {\"Head\": \"users\", \"Relation\": \"administer\", \"Tail\": \"company's Bank Baskets\"}, {\"Head\": \"<EMAIL>\", \"Relation\": \"can set up\", \"Tail\": \"relevant administrative rights\"}, {\"Head\": \"customer relationship manager\", \"Relation\": \"can set up\", \"Tail\": \"relevant administrative rights\"}, {\"Head\": \"Bank Basket configuration\", \"Relation\": \"found within\", \"Tail\": \"Bridge Administration tool\"}, {\"Head\": \"Bridge Administration\", \"Relation\": \"accessed via\", \"Tail\": \"menu option \\\"Administration\\\"\"}, {\"Head\": \"menu option \\\"Administration\\\"\", \"Relation\": \"located in\", \"Tail\": \"screen header\"}, {\"Head\": \"screen header\", \"Relation\": \"is part of\", \"Tail\": \"Bridge application\"}, {\"Head\": \"Bridge Administration feature\", \"Relation\": \"opens to\", \"Tail\": \"homepage\"}, {\"Head\": \"homepage\", \"Relation\": \"has\", \"Tail\": \"available shortcuts\"}, {\"Head\": \"available shortcuts\", \"Relation\": \"lead to\", \"Tail\": \"different configuration tools\"}, {\"Head\": \"different configuration tools\", \"Relation\": \"are for\", \"Tail\": \"particular user\"}, {\"Head\": \"quick navigation toolbar\", \"Relation\": \"shows\", \"Tail\": \"active homepage icon\"}, {\"Head\": \"quick navigation toolbar\", \"Relation\": \"available on\", \"Tail\": \"left side of the homepage\"}, {\"Head\": \"image\", \"Relation\": \"shows\", \"Tail\": \"screenshot\"}, {\"Head\": \"screenshot\", \"Relation\": \"is of\", \"Tail\": \"360T Bank Baskets Configuration user guide\"}, {\"Head\": \"screen\", \"Relation\": \"displays\", \"Tail\": \"'Administration Start' page\"}, {\"Head\": \"'Administration Start' page\", \"Relation\": \"has option for\", \"Tail\": \"'Regulatory Data'\"}, {\"Head\": \"'Administration Start' page\", \"Relation\": \"has option for\", \"Tail\": \"'Bank Baskets'\"}, {\"Head\": \"'Administration Start' page\", \"Relation\": \"has option for\", \"Tail\": \"'Change Request'\"}, {\"Head\": \"'Administration Start' page\", \"Relation\": \"has option for\", \"Tail\": \"'Wizards'\"}, {\"Head\": \"'Administration Start' page\", \"Relation\": \"has option for\", \"Tail\": \"'Evaluator Tools'\"}, {\"Head\": \"top of the screen\", \"Relation\": \"includes\", \"Tail\": \"tabs\"}, {\"Head\": \"tabs\", \"Relation\": \"include\", \"Tail\": \"'RFS REQUESTER'\"}, {\"Head\": \"tabs\", \"Relation\": \"include\", \"Tail\": \"'DEAL TRACKING'\"}, {\"Head\": \"tabs\", \"Relation\": \"include\", \"Tail\": \"'BRIDGE ADMINISTRATION'\"}, {\"Head\": \"top of the screen\", \"Relation\": \"includes\", \"Tail\": \"preferences options\"}, {\"Head\": \"top of the screen\", \"Relation\": \"includes\", \"Tail\": \"help options\"}]", "output_stage_two": "[{\"Event\": \"This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.\", \"Entity\": [\"user manual\", \"Bank Basket feature\", \"360T Bridge Administration tool\"]}, {\"Event\": \"The Bank Basket feature has been enhanced.\", \"Entity\": [\"Bank Basket feature\"]}, {\"Event\": \"The enhancement provides improved rule management capabilities.\", \"Entity\": [\"enhancement\", \"rule management capabilities\"]}, {\"Event\": \"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.\", \"Entity\": [\"configuration groups\", \"currency\", \"currency couple\", \"time period\", \"product(s)\"]}, {\"Event\": \"Separate baskets can be configured by request type.\", \"Entity\": [\"separate baskets\", \"request type\"]}, {\"Event\": \"Request types include RFS.\", \"Entity\": [\"RFS\", \"request types\"]}, {\"Event\": \"Request types include Order.\", \"Entity\": [\"Order\", \"request types\"]}, {\"Event\": \"Request types include SEP.\", \"Entity\": [\"SEP\", \"request types\"]}, {\"Event\": \"Temporary blocks can be applied.\", \"Entity\": [\"temporary blocks\"]}, {\"Event\": \"Temporary blocks can be removed.\", \"Entity\": [\"temporary blocks\"]}, {\"Event\": \"Applying temporary blocks does not affect the configured rules.\", \"Entity\": [\"temporary blocks\", \"configured rules\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 523, "time": 1.0}, "usage_stage_two": {"completion_tokens": 172, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "s system information and connection status.\" } Figure 2 Bridge Administration: Homepage The \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree. Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity. | | | | | | | | | Preferences | Administration | Help | A A | |--|--|----------------------|----------------------|------------------------------|---|--|--|-------------|----------------|------|-----| | | | <b>RFS Requester</b> | <b>DEAL TRACKING</b> | <b>Bridge Administration</b> | + | | | | | | | Q 360T.ALIAS 360T.ALIAS.Company 1 360T.ALIAS.Company 2 360T.ALIAS.Company 3 360T.ALIAS.Company 4 360T.ALIAS.Company 5 360T.ALIAS.Company 6 >> No Individuals/Institutions are selected | 360TAS Treasurer 1, 360T ALIAS // INT | Mi, 25. Apr 2018, 14:42:22 GMT // Connected | |---------------------------------------|---------------------------------------------| |---------------------------------------|---------------------------------------------| Figure 3 Bank Basket: Start page The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity. It is possible to open multiple forms/sheets at a time. The selected item will be highlighted as an active task inside the taskbar. Image /page/5/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The interface includes a navigation menu on the left, a main content area in the center, and a header with user preferences and administration options. The navigation menu lists several 360T.ALIAS companies. The main content area displays currency group settings, with options to create and manage currency groups. The header includes tabs for different configuration groups, such as RFS Bank Baskets, Orders Bank Baskets, and SEP Bank Baskets.\" } Figure 4 Bank Basket: Configuration for a selected legal entity A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel: - Search : A search field will open and the user can type in an alphanumeric value in order to find the desired institution. - Scroll from source : This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel. Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source. - Show individuals view toggle : This icon is deactivated when using the Bank Basket configuration. For other configuration tools the toggle option allows the user to display only individuals in the navigation panel. - Show institutions view toggle : This icon is deactivated when using t", "entity_relation_dict": [{"Head": "Figure 2", "Relation": "depicts", "Tail": "Bridge Administration Homepage"}, {"Head": "\"Bank Baskets\" quick link", "Relation": "opens", "Tail": "navigation panel"}, {"Head": "navigation panel", "Relation": "contains", "Tail": "institution tree"}, {"Head": "institution tree", "Relation": "includes", "Tail": "single TEX entity"}, {"Head": "institution tree", "Relation": "includes", "Tail": "TEX main entity"}, {"Head": "trade-as entities", "Relation": "configured under", "Tail": "TEX main entity"}, {"Head": "trade-on-behalf entities", "Relation": "configured under", "Tail": "TEX main entity"}], "event_entity_relation_dict": [{"Event": "The \"Bank Baskets\" quick link opens a navigation panel which contains an institution tree.", "Entity": ["Bank Baskets quick link", "navigation panel", "institution tree"]}, {"Event": "Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.", "Entity": ["tree", "TEX entity", "TEX main entity", "trade-as", "trade-on-behalf", "ITEX entities", "main entity"]}, {"Event": "The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.", "Entity": ["institution", "single-click", "institution tree", "new form/sheet", "Bank Basket configuration details", "entity"]}, {"Event": "It is possible to open multiple forms/sheets at a time.", "Entity": ["multiple forms/sheets"]}, {"Event": "The selected item will be highlighted as an active task inside the taskbar.", "Entity": ["selected item", "active task", "taskbar"]}, {"Event": "A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:", "Entity": ["set of icons", "single-click", "navigation panel"]}, {"Event": "A search field will open and the user can type in an alphanumeric value in order to find the desired institution.", "Entity": ["search field", "user", "alphanumeric value", "institution"]}, {"Event": "This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.", "Entity": ["This feature", "user", "active task/sheet", "navigation panel"]}, {"Event": "Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.", "Entity": ["selected tree item", "active institution", "taskbar", "scroll from source"]}, {"Event": "This icon is deactivated when using the Bank Basket configuration.", "Entity": ["This icon", "Bank Basket configuration"]}, {"Event": "For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.", "Entity": ["other configuration tools", "toggle option", "user", "individuals", "navigation panel"]}], "event_relation_dict": [], "output_stage_one": "[{\"Head\": \"Figure 2\", \"Relation\": \"depicts\", \"Tail\": \"Bridge Administration Homepage\"}, {\"Head\": \"\\\"Bank Baskets\\\" quick link\", \"Relation\": \"opens\", \"Tail\": \"navigation panel\"}, {\"Head\": \"navigation panel\", \"Relation\": \"contains\", \"Tail\": \"institution tree\"}, {\"Head\": \"institution tree\", \"Relation\": \"includes\", \"Tail\": \"single TEX entity\"}, {\"Head\": \"institution tree\", \"Relation\": \"includes\", \"Tail\": \"TEX main entity\"}, {\"Head\": \"trade-as entities\", \"Relation\": \"configured under\", \"Tail\": \"TEX main entity\"}, {\"Head\": \"trade-on-behalf entities\", \"Relation\": \"configured under\", \"Tail\": \"TEX main entity\"}]", "output_stage_two": "[{\"Event\": \"The \\\"Bank Baskets\\\" quick link opens a navigation panel which contains an institution tree.\", \"Entity\": [\"Bank Baskets quick link\", \"navigation panel\", \"institution tree\"]}, {\"Event\": \"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.\", \"Entity\": [\"tree\", \"TEX entity\", \"TEX main entity\", \"trade-as\", \"trade-on-behalf\", \"ITEX entities\", \"main entity\"]}, {\"Event\": \"The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.\", \"Entity\": [\"institution\", \"single-click\", \"institution tree\", \"new form/sheet\", \"Bank Basket configuration details\", \"entity\"]}, {\"Event\": \"It is possible to open multiple forms/sheets at a time.\", \"Entity\": [\"multiple forms/sheets\"]}, {\"Event\": \"The selected item will be highlighted as an active task inside the taskbar.\", \"Entity\": [\"selected item\", \"active task\", \"taskbar\"]}, {\"Event\": \"A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:\", \"Entity\": [\"set of icons\", \"single-click\", \"navigation panel\"]}, {\"Event\": \"A search field will open and the user can type in an alphanumeric value in order to find the desired institution.\", \"Entity\": [\"search field\", \"user\", \"alphanumeric value\", \"institution\"]}, {\"Event\": \"This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.\", \"Entity\": [\"This feature\", \"user\", \"active task/sheet\", \"navigation panel\"]}, {\"Event\": \"Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.\", \"Entity\": [\"selected tree item\", \"active institution\", \"taskbar\", \"scroll from source\"]}, {\"Event\": \"This icon is deactivated when using the Bank Basket configuration.\", \"Entity\": [\"This icon\", \"Bank Basket configuration\"]}, {\"Event\": \"For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.\", \"Entity\": [\"other configuration tools\", \"toggle option\", \"user\", \"individuals\", \"navigation panel\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 88, "time": 1.0}, "usage_stage_two": {"completion_tokens": 331, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "he Bank Basket configuration. For other configuration tools the toggle option allows the user to display only institutions in the navigation panel. The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel. Each entity tab has a Live Audit Log which tracks all unsaved changes. Individual unsaved changes can be reverted by clicking on the arrow icon. Clicking on the \"Discard all changes\" button will revert all unsaved changes. Image /page/6/Picture/0 description: { \"image\\_description\": \"The image contains the text 'User Guide 360T Bank Baskets Configuration'. The text appears to be the title or heading of a document or guide.\" } | | 9 ※ 上 | | | | | | Live Audit Log | 目<br>$\\Omega$ | |-----------------------------|------------------------------------------|---------------------------------------------------------|-----------------------------------|------------------------------------------|---|------------|------------------|---------------| | $\\mathcal{L}_{\\mathcal{F}}$ | へ 盒 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | <b>Currency Groups</b><br><b>Currency Couple Groups</b> | FX Time Period Groups | MM Time Period ( ) | | Target | Event Name | | | | 直 360T.ALIAS.Company 2 | | | | | 360T.ALIAS | Currency Added | $\\sqrt{2}$ | | ₿ | 盒 360T.ALIAS.Company 3 | <b>Currency Group</b> | | | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | | 盒 360T.ALIAS.Company 4 | Default Group | | a <sub>1</sub> | | 360T ALIAS | Currency Added | $\\sqrt{2}$ | | <b>tip</b> | 宜 360T.ALIAS.Company 5 | | | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | | 盒 360T.ALIAS.Company 6 | | | | | 360T.ALIAS | Currency Added | $\\mathcal{L}$ | | 同 | | | | | | 360T.ALIAS | Currency Added | $\\sqrt{2}$ | | | | | | | √ | 360T.ALIAS | Currency Removed | | | $\\vec{\\Sigma}$ | | | | | | | | | | ಥೆ | | | | | | | | | | | | | | | | | | | | | | | | <b>DX</b><br>$ 1^{2}$ | | | | | | | | Available Currencies | Select Member for \"Default Group\" | <b>Selected Currencies</b> | | | | | | | | | AUD | | | | | | | | | SEK | $\\geq$<br>CAD | | | | | | | | | SGD<br>ZAR | $\\overline{\\epsilon}$<br>CHF | | | | | | | | | AED | EUR | | | | | | | | | AFN | GBP | | | | | | | | | ALL | $\\gg$<br><b>USD</b> | | | | | | | | | AMD | $\\ll$ | | | | | | | | | ANG | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Figure 5 Bank Basket: Configuration: Live Audit Log ## 3 CONFIGURATION OF BANK BASKETS Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs: - Configuration Groups: Facilitate centralized management of parameters that can be used in each Bank Basket configuration (RFS; Order; SEP). Allow users to create a group one single time and reuse it across various rules. - RFS Bank Baskets: Provider Groups and temporarily Blocked Providers ma", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The toggle option allows the user to display only institutions in the navigation panel.", "Entity": ["toggle option", "user", "institutions", "navigation panel", "configuration tools"]}, {"Event": "The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.", "Entity": ["navigation panel", "minimize icon", "panel"]}, {"Event": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "Entity": ["entity tab", "Live Audit Log", "unsaved changes"]}, {"Event": "Individual unsaved changes can be reverted by clicking on the arrow icon.", "Entity": ["unsaved changes", "arrow icon"]}, {"Event": "Clicking on the \"Discard all changes\" button will revert all unsaved changes.", "Entity": ["\"Discard all changes\" button", "unsaved changes"]}, {"Event": "The image contains the text 'User Guide 360T Bank Baskets Configuration'.", "Entity": ["image", "text 'User Guide 360T Bank Baskets Configuration'"]}, {"Event": "Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.", "Entity": ["entity", "institution tree", "Configuration Groups tab", "data tabs"]}, {"Event": "Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.", "Entity": ["Configuration Groups", "parameters", "Bank Basket configuration", "RFS", "Order", "SEP"]}, {"Event": "Configuration Groups allow users to create a group one single time and reuse it across various rules.", "Entity": ["Configuration Groups", "users", "group", "rules"]}], "event_relation_dict": [{"Head": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "Relation": "as a result", "Tail": "Individual unsaved changes can be reverted by clicking on the arrow icon."}, {"Head": "Each entity tab has a Live Audit Log which tracks all unsaved changes.", "Relation": "as a result", "Tail": "Clicking on the \"Discard all changes\" button will revert all unsaved changes."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The toggle option allows the user to display only institutions in the navigation panel.\", \"Entity\": [\"toggle option\", \"user\", \"institutions\", \"navigation panel\", \"configuration tools\"]}, {\"Event\": \"The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.\", \"Entity\": [\"navigation panel\", \"minimize icon\", \"panel\"]}, {\"Event\": \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\", \"Entity\": [\"entity tab\", \"Live Audit Log\", \"unsaved changes\"]}, {\"Event\": \"Individual unsaved changes can be reverted by clicking on the arrow icon.\", \"Entity\": [\"unsaved changes\", \"arrow icon\"]}, {\"Event\": \"Clicking on the \\\"Discard all changes\\\" button will revert all unsaved changes.\", \"Entity\": [\"\\\"Discard all changes\\\" button\", \"unsaved changes\"]}, {\"Event\": \"The image contains the text 'User Guide 360T Bank Baskets Configuration'.\", \"Entity\": [\"image\", \"text 'User Guide 360T Bank Baskets Configuration'\"]}, {\"Event\": \"Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.\", \"Entity\": [\"entity\", \"institution tree\", \"Configuration Groups tab\", \"data tabs\"]}, {\"Event\": \"Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.\", \"Entity\": [\"Configuration Groups\", \"parameters\", \"Bank Basket configuration\", \"RFS\", \"Order\", \"SEP\"]}, {\"Event\": \"Configuration Groups allow users to create a group one single time and reuse it across various rules.\", \"Entity\": [\"Configuration Groups\", \"users\", \"group\", \"rules\"]}]", "output_stage_three": "[{\"Head\": \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\", \"Relation\": \"as a result\", \"Tail\": \"Individual unsaved changes can be reverted by clicking on the arrow icon.\"}, {\"Head\": \"Each entity tab has a Live Audit Log which tracks all unsaved changes.\", \"Relation\": \"as a result\", \"Tail\": \"Clicking on the \\\"Discard all changes\\\" button will revert all unsaved changes.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 231, "time": 1.0}, "usage_stage_three": {"completion_tokens": 70, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "y be configured specifically for RFS requests. Bank Basket rules for four separate request types are configured on this tab: - o RFS FX Bank Baskets - o RFS MM Bank Baskets - o RFS Commodity Bank Baskets - o RFS Cross Currency Netting Bank Baskets - Order Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Orders. Bank Basket rules for Forward or Spot orders are configured on this tab. - SEP Bank Baskets: Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP). Bank Basket rules for SEP streaming spot executions are configured on this tab. Image /page/7/Picture/0 description: { \"image\\_description\": \"The image shows the logo of DEUTSCHE GROUP. The logo consists of two parts: a green graphic element on the left and the text \\\"DEUTSCHE GROUP\\\" on the right. The graphic element is a stylized representation of the letters \\\"360\\\" and a \\\"T\\\" inside a rounded rectangle. The text \\\"DEUTSCHE GROUP\\\" is in a sans-serif font, with \\\"DEUTSCHE\\\" stacked above \\\"GROUP\\\".\" } | RFS Requester | DEAL TRACKING | Bridge Administration | + | Preferences | Administration | Help | AA | X | |---------------|---------------|-----------------------|---|-------------|----------------|------|----|---| |---------------|---------------|-----------------------|---|-------------|----------------|------|----|---| | | | <input type=\"text\"/> | |--|--|----------------------| |--|--|----------------------| | | 360T.ALIAS | |--|----------------------| | | 360T.ALIAS.Company 1 | | | 360T.ALIAS Company 2 | | | 360T.ALIAS.Company 3 | | | 360T.ALIAS.Company 4 | | | 360T.ALIAS.Company 5 | | | 360T.ALIAS.Company 6 | | Configuration Groups | RFS Bank Baskets | Orders Bank Baskets | SEP Bank Baskets | | |----------------------|------------------------|-----------------------|-----------------------|----------------| | Currency Groups | Currency Couple Groups | FX Time Period Groups | MM Time Period Groups | Product Groups | | Currency Group | |----------------| | Default Group | | | | Create Group | |--|--|--------------| |--|--|--------------| | Create Change Request | Discard All Changes | Save | |-----------------------|---------------------|------| |-----------------------|---------------------|------| | 360T.ALIAS Company 1 X | |------------------------| |------------------------| | 360TAS Treasurer 1, 360T ALIAS // INT | Tue, 19. Jun 2018, 15:59:16 GMT // Connected [FFM] | |---------------------------------------|----------------------------------------------------| |---------------------------------------|----------------------------------------------------| Figure 6 Bank Basket: Configuration Data Tabs Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another i.e., an RFS configuration will have no impact on SEP trading, and vice v", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "Bank Basket rules for four separate request types are configured on this tab.", "Entity": ["Bank Basket rules", "request types", "this tab", "RFS FX Bank Baskets", "RFS MM Bank Baskets", "RFS Commodity Bank Baskets", "RFS Cross Currency Netting Bank Baskets"]}, {"Event": "Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.", "Entity": ["Provider Groups", "Blocked Providers", "Orders", "Order Bank Baskets"]}, {"Event": "Bank Basket rules for Forward or Spot orders are configured on this tab.", "Entity": ["Bank Basket rules", "Forward orders", "Spot orders", "this tab"]}, {"Event": "Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).", "Entity": ["Provider Groups", "Blocked Providers", "Supersonic (SEP)", "SEP Bank Baskets"]}, {"Event": "Bank Basket rules for SEP streaming spot executions are configured on this tab.", "Entity": ["Bank Basket rules", "SEP streaming spot executions", "this tab"]}, {"Event": "Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.", "Entity": ["Bank Basket Configurations", "RFS requests", "Orders requests", "SEP requests"]}, {"Event": "An RFS configuration will have no impact on SEP trading.", "Entity": ["RFS configuration", "SEP trading"]}], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"Bank Basket rules for four separate request types are configured on this tab.\", \"Entity\": [\"Bank Basket rules\", \"request types\", \"this tab\", \"RFS FX Bank Baskets\", \"RFS MM Bank Baskets\", \"RFS Commodity Bank Baskets\", \"RFS Cross Currency Netting Bank Baskets\"]}, {\"Event\": \"Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.\", \"Entity\": [\"Provider Groups\", \"Blocked Providers\", \"Orders\", \"Order Bank Baskets\"]}, {\"Event\": \"Bank Basket rules for Forward or Spot orders are configured on this tab.\", \"Entity\": [\"Bank Basket rules\", \"Forward orders\", \"Spot orders\", \"this tab\"]}, {\"Event\": \"Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).\", \"Entity\": [\"Provider Groups\", \"Blocked Providers\", \"Supersonic (SEP)\", \"SEP Bank Baskets\"]}, {\"Event\": \"Bank Basket rules for SEP streaming spot executions are configured on this tab.\", \"Entity\": [\"Bank Basket rules\", \"SEP streaming spot executions\", \"this tab\"]}, {\"Event\": \"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.\", \"Entity\": [\"Bank Basket Configurations\", \"RFS requests\", \"Orders requests\", \"SEP requests\"]}, {\"Event\": \"An RFS configuration will have no impact on SEP trading.\", \"Entity\": [\"RFS configuration\", \"SEP trading\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 209, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "ersa. ### 3.1 Setting up Configuration Groups Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration. Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP. The groups themselves can be edited (values added or removed) without changing a set of rules based on those groups. The available parameters are: - o Currency Groups - o Currency Couple Groups - o FX Time Period Groups - o MM Time Period Groups A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes. - Create Group : To add a new group. - Rename Group : To change the name of a group. Cannot be used on the Default Group. - Remove Group : To delete an individual group. Cannot be used on the Default Group. If the removed group is used in any configured rules this group is replaced by the Default Group. - Save : Please remember to save changes to your configurations. Configuration Groups are particularly useful when configuring complex bank basket rules. - Note: It is not required to configure groups based on the above parameters. Users may still set individual custom rules without utilizing the Configuration Groups. Individual custom rules may be preferable for some users with less complex bank basket setups. - Note: Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group. The Default Group will include all existing values. All Default Groups can be modified (values may be removed). In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group. For example, if a new currency is added to the 360T platform the Default Currency Group will not include the new currency. The new currency must be selected by the user. #### 3.1.1 Defining Currency Groups Currency Groups are intended to allow the classification of single currencies into customized groups. This allows setting one single rule for each group of currencies rather than many rules for individual currencies. Currencies can be added or removed from the group without editing the rules themselves. Image /page/8/Picture/11 description: { \"image\\_description\": \"The image shows a user interface for managing currency groups in a financial application. The interface includes a list of available currencies and a list of selected currencies, allowing users to add or remove currencies from a group. The user is currently creating a currency group named G10.\" } Figure 7 Bank Basket: Currency Groups Once created, a currency group can be used to simplify rule creation for (1) interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and ", "entity_relation_dict": [{"Head": "Configuration Groups", "Relation": "facilitate", "Tail": "centralized management"}, {"Head": "parameters", "Relation": "used in", "Tail": "Bank Basket configuration"}, {"Head": "Groups", "Relation": "configured in", "Tail": "each parameter"}, {"Head": "Groups", "Relation": "reused for", "Tail": "creating rules"}, {"Head": "groups", "Relation": "edited without changing", "Tail": "set of rules"}, {"Head": "available parameters", "Relation": "include", "Tail": "Currency Groups"}, {"Head": "available parameters", "Relation": "include", "Tail": "Currency Couple Groups"}, {"Head": "available parameters", "Relation": "include", "Tail": "FX Time Period Groups"}, {"Head": "available parameters", "Relation": "include", "Tail": "MM Time Period Groups"}, {"Head": "icons", "Relation": "allow user to", "Tail": "create new group"}, {"Head": "icons", "Relation": "allow user to", "Tail": "edit name of existing group"}, {"Head": "icons", "Relation": "allow user to", "Tail": "delete group"}, {"Head": "icons", "Relation": "allow user to", "Tail": "save changes"}, {"Head": "Rename Group", "Relation": "cannot be used on", "Tail": "Default Group"}, {"Head": "Remove Group", "Relation": "cannot be used on", "Tail": "Default Group"}, {"Head": "removed group", "Relation": "replaced by", "Tail": "Default Group"}, {"Head": "Configuration Groups", "Relation": "useful for", "Tail": "configuring complex bank basket rules"}, {"Head": "Users", "Relation": "set", "Tail": "individual custom rules"}, {"Head": "Individual custom rules", "Relation": "preferable for", "Tail": "users with less complex bank basket setups"}, {"Head": "Bank Basket Configuration", "Relation": "contains", "Tail": "Default Group"}, {"Head": "Default Group", "Relation": "includes", "Tail": "existing values"}, {"Head": "Default Groups", "Relation": "modified by removing", "Tail": "values"}, {"Head": "new values", "Relation": "not added to", "Tail": "Default Group"}, {"Head": "new currency", "Relation": "not included in", "Tail": "Default Currency Group"}, {"Head": "new currency", "Relation": "selected by", "Tail": "user"}, {"Head": "Currency Groups", "Relation": "allow classification of", "Tail": "single currencies"}, {"Head": "Currency Groups", "Relation": "allow setting", "Tail": "single rule for group of currencies"}, {"Head": "Currencies", "Relation": "added or removed from", "Tail": "group"}, {"Head": "currency group", "Relation": "simplifies", "Tail": "rule creation for interest rate products"}, {"Head": "interest rate products", "Relation": "include", "Tail": "Loan"}, {"Head": "interest rate products", "Relation": "include", "Tail": "Deposit"}, {"Head": "interest rate products", "Relation": "include", "Tail": "Interest Rate Swap"}, {"Head": "interest rate products", "Relation": "include", "Tail": "FRA"}, {"Head": "interest rate products", "Relation": "include", "Tail": "CapFloor"}], "event_entity_relation_dict": [{"Event": "Configuration Groups facilitate centralized management of parameters.", "Entity": ["Configuration Groups", "centralized management", "parameters", "Bank Basket configuration"]}, {"Event": "Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.", "Entity": ["Groups", "parameters", "rules", "requests", "RFS", "Orders", "SEP"]}, {"Event": "The groups themselves can be edited without changing a set of rules based on those groups.", "Entity": ["groups", "values", "rules"]}, {"Event": "The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.", "Entity": ["parameters", "Currency Groups", "Currency Couple Groups", "FX Time Period Groups", "MM Time Period Groups"]}, {"Event": "A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.", "Entity": ["icons", "Configuration Group area", "user", "group", "name", "changes"]}, {"Event": "Create Group adds a new group.", "Entity": ["Create Group", "group"]}, {"Event": "Rename Group changes the name of a group.", "Entity": ["Rename Group", "name", "group"]}, {"Event": "Rename Group cannot be used on the Default Group.", "Entity": ["Rename Group", "Default Group"]}, {"Event": "Remove Group deletes an individual group.", "Entity": ["Remove Group", "group"]}, {"Event": "Remove Group cannot be used on the Default Group.", "Entity": ["Remove Group", "Default Group"]}, {"Event": "If the removed group is used in any configured rules this group is replaced by the Default Group.", "Entity": ["removed group", "configured rules", "Default Group"]}, {"Event": "Users are reminded to save changes to configurations.", "Entity": ["users", "changes", "configurations"]}, {"Event": "Configuration Groups are particularly useful when configuring complex bank basket rules.", "Entity": ["Configuration Groups", "complex bank basket rules"]}, {"Event": "It is not required to configure groups based on the above parameters.", "Entity": ["groups", "parameters"]}, {"Event": "Users may still set individual custom rules without utilizing the Configuration Groups.", "Entity": ["Users", "individual custom rules", "Configuration Groups"]}, {"Event": "Individual custom rules may be preferable for some users with less complex bank basket setups.", "Entity": ["Individual custom rules", "users", "bank basket setups"]}, {"Event": "Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.", "Entity": ["Bank Basket Configuration", "parameters", "Default Group"]}, {"Event": "The Default Group will include all existing values.", "Entity": ["Default Group", "existing values"]}, {"Event": "All Default Groups can be modified.", "Entity": ["Default Groups", "values"]}, {"Event": "In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.", "Entity": ["tool", "values", "versions", "Default Group"]}, {"Event": "If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.", "Entity": ["new currency", "360T platform", "Default Currency Group"]}, {"Event": "The new currency must be selected by the user.", "Entity": ["new currency", "user"]}, {"Event": "Currency Groups are intended to allow the classification of single currencies into customized groups.", "Entity": ["Currency Groups", "single currencies", "customized groups"]}, {"Event": "This allows setting one single rule for each group of currencies rather than many rules for individual currencies.", "Entity": ["rule", "group of currencies", "individual currencies"]}, {"Event": "Currencies can be added or removed from the group without editing the rules themselves.", "Entity": ["Currencies", "group", "rules"]}, {"Event": "The image shows a user interface for managing currency groups in a financial application.", "Entity": ["image", "user"]}], "event_relation_dict": [{"Head": "Configuration Groups facilitate centralized management of parameters.", "Relation": "as a result", "Tail": "Groups can be configured once and reused when creating various rules."}, {"Head": "Groups can be configured once and reused when creating various rules.", "Relation": "at the same time", "Tail": "The groups themselves can be edited without changing a set of rules based on those groups."}, {"Head": "The Rename Group function is to change the name of a group.", "Relation": "at the same time", "Tail": "The Rename Group function cannot be used on the Default Group."}, {"Head": "The Remove Group function is to delete an individual group.", "Relation": "at the same time", "Tail": "The Remove Group function cannot be used on the Default Group."}, {"Head": "A group is removed.", "Relation": "as a result", "Tail": "The removed group is replaced by the Default Group if it is used in any configured rules."}, {"Head": "It is not required to configure groups based on the above parameters.", "Relation": "as a result", "Tail": "Users may still set individual custom rules without utilizing the Configuration Groups."}], "output_stage_one": "[{\"Head\": \"Configuration Groups\", \"Relation\": \"facilitate\", \"Tail\": \"centralized management\"}, {\"Head\": \"parameters\", \"Relation\": \"used in\", \"Tail\": \"Bank Basket configuration\"}, {\"Head\": \"Groups\", \"Relation\": \"configured in\", \"Tail\": \"each parameter\"}, {\"Head\": \"Groups\", \"Relation\": \"reused for\", \"Tail\": \"creating rules\"}, {\"Head\": \"groups\", \"Relation\": \"edited without changing\", \"Tail\": \"set of rules\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"Currency Groups\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"Currency Couple Groups\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"FX Time Period Groups\"}, {\"Head\": \"available parameters\", \"Relation\": \"include\", \"Tail\": \"MM Time Period Groups\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"create new group\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"edit name of existing group\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"delete group\"}, {\"Head\": \"icons\", \"Relation\": \"allow user to\", \"Tail\": \"save changes\"}, {\"Head\": \"Rename Group\", \"Relation\": \"cannot be used on\", \"Tail\": \"Default Group\"}, {\"Head\": \"Remove Group\", \"Relation\": \"cannot be used on\", \"Tail\": \"Default Group\"}, {\"Head\": \"removed group\", \"Relation\": \"replaced by\", \"Tail\": \"Default Group\"}, {\"Head\": \"Configuration Groups\", \"Relation\": \"useful for\", \"Tail\": \"configuring complex bank basket rules\"}, {\"Head\": \"Users\", \"Relation\": \"set\", \"Tail\": \"individual custom rules\"}, {\"Head\": \"Individual custom rules\", \"Relation\": \"preferable for\", \"Tail\": \"users with less complex bank basket setups\"}, {\"Head\": \"Bank Basket Configuration\", \"Relation\": \"contains\", \"Tail\": \"Default Group\"}, {\"Head\": \"Default Group\", \"Relation\": \"includes\", \"Tail\": \"existing values\"}, {\"Head\": \"Default Groups\", \"Relation\": \"modified by removing\", \"Tail\": \"values\"}, {\"Head\": \"new values\", \"Relation\": \"not added to\", \"Tail\": \"Default Group\"}, {\"Head\": \"new currency\", \"Relation\": \"not included in\", \"Tail\": \"Default Currency Group\"}, {\"Head\": \"new currency\", \"Relation\": \"selected by\", \"Tail\": \"user\"}, {\"Head\": \"Currency Groups\", \"Relation\": \"allow classification of\", \"Tail\": \"single currencies\"}, {\"Head\": \"Currency Groups\", \"Relation\": \"allow setting\", \"Tail\": \"single rule for group of currencies\"}, {\"Head\": \"Currencies\", \"Relation\": \"added or removed from\", \"Tail\": \"group\"}, {\"Head\": \"currency group\", \"Relation\": \"simplifies\", \"Tail\": \"rule creation for interest rate products\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"Loan\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"Deposit\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"Interest Rate Swap\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"FRA\"}, {\"Head\": \"interest rate products\", \"Relation\": \"include\", \"Tail\": \"CapFloor\"}]", "output_stage_two": "[{\"Event\": \"Configuration Groups facilitate centralized management of parameters.\", \"Entity\": [\"Configuration Groups\", \"centralized management\", \"parameters\", \"Bank Basket configuration\"]}, {\"Event\": \"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.\", \"Entity\": [\"Groups\", \"parameters\", \"rules\", \"requests\", \"RFS\", \"Orders\", \"SEP\"]}, {\"Event\": \"The groups themselves can be edited without changing a set of rules based on those groups.\", \"Entity\": [\"groups\", \"values\", \"rules\"]}, {\"Event\": \"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.\", \"Entity\": [\"parameters\", \"Currency Groups\", \"Currency Couple Groups\", \"FX Time Period Groups\", \"MM Time Period Groups\"]}, {\"Event\": \"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.\", \"Entity\": [\"icons\", \"Configuration Group area\", \"user\", \"group\", \"name\", \"changes\"]}, {\"Event\": \"Create Group adds a new group.\", \"Entity\": [\"Create Group\", \"group\"]}, {\"Event\": \"Rename Group changes the name of a group.\", \"Entity\": [\"Rename Group\", \"name\", \"group\"]}, {\"Event\": \"Rename Group cannot be used on the Default Group.\", \"Entity\": [\"Rename Group\", \"Default Group\"]}, {\"Event\": \"Remove Group deletes an individual group.\", \"Entity\": [\"Remove Group\", \"group\"]}, {\"Event\": \"Remove Group cannot be used on the Default Group.\", \"Entity\": [\"Remove Group\", \"Default Group\"]}, {\"Event\": \"If the removed group is used in any configured rules this group is replaced by the Default Group.\", \"Entity\": [\"removed group\", \"configured rules\", \"Default Group\"]}, {\"Event\": \"Users are reminded to save changes to configurations.\", \"Entity\": [\"users\", \"changes\", \"configurations\"]}, {\"Event\": \"Configuration Groups are particularly useful when configuring complex bank basket rules.\", \"Entity\": [\"Configuration Groups\", \"complex bank basket rules\"]}, {\"Event\": \"It is not required to configure groups based on the above parameters.\", \"Entity\": [\"groups\", \"parameters\"]}, {\"Event\": \"Users may still set individual custom rules without utilizing the Configuration Groups.\", \"Entity\": [\"Users\", \"individual custom rules\", \"Configuration Groups\"]}, {\"Event\": \"Individual custom rules may be preferable for some users with less complex bank basket setups.\", \"Entity\": [\"Individual custom rules\", \"users\", \"bank basket setups\"]}, {\"Event\": \"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.\", \"Entity\": [\"Bank Basket Configuration\", \"parameters\", \"Default Group\"]}, {\"Event\": \"The Default Group will include all existing values.\", \"Entity\": [\"Default Group\", \"existing values\"]}, {\"Event\": \"All Default Groups can be modified.\", \"Entity\": [\"Default Groups\", \"values\"]}, {\"Event\": \"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.\", \"Entity\": [\"tool\", \"values\", \"versions\", \"Default Group\"]}, {\"Event\": \"If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.\", \"Entity\": [\"new currency\", \"360T platform\", \"Default Currency Group\"]}, {\"Event\": \"The new currency must be selected by the user.\", \"Entity\": [\"new currency\", \"user\"]}, {\"Event\": \"Currency Groups are intended to allow the classification of single currencies into customized groups.\", \"Entity\": [\"Currency Groups\", \"single currencies\", \"customized groups\"]}, {\"Event\": \"This allows setting one single rule for each group of currencies rather than many rules for individual currencies.\", \"Entity\": [\"rule\", \"group of currencies\", \"individual currencies\"]}, {\"Event\": \"Currencies can be added or removed from the group without editing the rules themselves.\", \"Entity\": [\"Currencies\", \"group\", \"rules\"]}, {\"Event\": \"The image shows a user interface for managing currency groups in a financial application.\", \"Entity\": [\"image\", \"user\"]}]", "output_stage_three": "[{\"Head\": \"Configuration Groups facilitate centralized management of parameters.\", \"Relation\": \"as a result\", \"Tail\": \"Groups can be configured once and reused when creating various rules.\"}, {\"Head\": \"Groups can be configured once and reused when creating various rules.\", \"Relation\": \"at the same time\", \"Tail\": \"The groups themselves can be edited without changing a set of rules based on those groups.\"}, {\"Head\": \"The Rename Group function is to change the name of a group.\", \"Relation\": \"at the same time\", \"Tail\": \"The Rename Group function cannot be used on the Default Group.\"}, {\"Head\": \"The Remove Group function is to delete an individual group.\", \"Relation\": \"at the same time\", \"Tail\": \"The Remove Group function cannot be used on the Default Group.\"}, {\"Head\": \"A group is removed.\", \"Relation\": \"as a result\", \"Tail\": \"The removed group is replaced by the Default Group if it is used in any configured rules.\"}, {\"Head\": \"It is not required to configure groups based on the above parameters.\", \"Relation\": \"as a result\", \"Tail\": \"Users may still set individual custom rules without utilizing the Configuration Groups.\"}]", "usage_stage_one": {"completion_tokens": 399, "time": 1.0}, "usage_stage_two": {"completion_tokens": 592, "time": 1.0}, "usage_stage_three": {"completion_tokens": 196, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "Tri Party Repo found in the RFS MM Bank Baskets area and (2) Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area. Image /page/9/Picture/0 description: { \"image\\_description\": \"The image shows a green and white logo with the number 360 and a symbol that looks like a T.\" } User Guide 360T Bank Baskets Configuration | 侖 | Q ※ 上 二<br>$\\langle$ | | <b>Configuration Groups</b> | <b>RFS Bank Baskets</b> | Orders Bank Baskets | SEP Bank Baskets Copy To | $ \\mathcal{E}_\\lambda $ | | | | の位置 | |----------------------------------|------------------------------------------------------------|--------------|-----------------------------|------------------------------|-----------------------------------------|--------------------------|----------------------------|--------------|-----------------------------------------|--------------|----------| | $\\mathcal{G}$ | $\\wedge$ $\\hat{\\Xi}$ 360T.ALIAS<br>盒 360T.ALIAS.Company 1 | | RFS Provider Groups | <b>Blocked RFS Providers</b> | RFS FX Bank Baskets RFS MM Bank Baskets | -- | RFS Commodity Bank Baskets | | RFS Cross Currency Netting Bank Baskets | | | | -<br>$\\Box$ | 盒 360T.ALIAS.Company 2 | | POS Product Type | | Currency | | <b>Time Period</b> | | <b>Bank Basket</b> | | | | 凾 | 盒 360T.ALIAS.Company 3<br><sup> 360T ALIAS Company 4</sup> | $\\mathbf{1}$ | Any | $\\vee$ | Any | $\\widehat{\\phantom{a}}$ | Any | $\\checkmark$ | Default Group | $\\checkmark$ | 図自 | | | 盒 360T ALIAS Company 5<br>章 360T ALIAS Company 6 | | | | Any<br>Default Group | | | | | | Add Rule | | $\\quad \\ \\ \\, \\textcircled{F}$ | | | | | 610. | | | | | | | | | | | | | Custom | | | | | | | | ಂದ್ರಿ | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Discard All Changes | <b>Save</b> | | Figure 8 Bank Basket: RFS MM Bank Baskets A default group exists which includes all currencies. This group cannot be removed or renamed. However, the currencies in the group can be altered as described below. | | | Preferences Administration Help AA - X | | |--|----------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|------| | | RFS REQUESTER DEAL TRACKING | + BRIDGE ADMINISTRATION | | | | Q | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To... | | | | 360T ALIAS | Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product Groups | | | | 360T.", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [{"Head": "Tri Party Repo found in the RFS MM Bank Baskets area.", "Relation": "at the same time", "Tail": "Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area."}, {"Head": "A default group exists which includes all currencies.", "Relation": "at the same time", "Tail": "This group cannot be removed or renamed."}, {"Head": "A default group exists which includes all currencies.", "Relation": "at the same time", "Tail": "The currencies in the group can be altered as described below."}, {"Head": "This group cannot be removed or renamed.", "Relation": "at the same time", "Tail": "The currencies in the group can be altered as described below."}], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[{\"Head\": \"Tri Party Repo found in the RFS MM Bank Baskets area.\", \"Relation\": \"at the same time\", \"Tail\": \"Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.\"}, {\"Head\": \"A default group exists which includes all currencies.\", \"Relation\": \"at the same time\", \"Tail\": \"This group cannot be removed or renamed.\"}, {\"Head\": \"A default group exists which includes all currencies.\", \"Relation\": \"at the same time\", \"Tail\": \"The currencies in the group can be altered as described below.\"}, {\"Head\": \"This group cannot be removed or renamed.\", \"Relation\": \"at the same time\", \"Tail\": \"The currencies in the group can be altered as described below.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 0, "time": 0}, "usage_stage_three": {"completion_tokens": 116, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "ALIAS Company 1<br>360T.ALIAS.Company 2<br>360T.ALIAS.Company 3<br>360T.ALIAS.Company 4<br>360T.ALIAS.Company 5<br>360T.ALIAS.Company 6 | Currency Group <div>Default Group</div> <div>G10</div> <div>Create Group</div> | | | | | | | | | | Select Member for \"Default Group\" | - X | | | | Available Currencies Selected Currencies <div>AUD</div> <div>CAD</div> <div>CHF</div> <div>EUR</div> <div>GBP</div> <div>HKD</div> <div>JPY</div> <div>NOK</div> | | | | | Discard All Changes Create Change Request 360T.ALIAS X | Save | | | 360TAS.Treasurer1, 360T ALIAS // INT | Fri, 06. Jul 2018, 07:39:04 GMT // Connected [FFM] | | Figure 9 Bank Basket: Currency Groups Default Group Currencies may be added or removed from the default group. A currency is highlighted with a single-click. This activates the single arrow. Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available. All currencies can be moved in either direction by using the double arrows. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Please, provide a name for a new group | | G10 | |--|-----| |--|-----| | Cancel | Create Group | |--------|--------------| |--------|--------------| Figure 10 Bank Basket: Currency Groups Create Group To view the currencies configured for the group, click on the Currency Group name. | Preferences | Administration | Help | AA | |-------------|----------------|------|----| |-------------|----------------|------|----| RFS REQUESTER DEAL TRACKING BRIDGE ADMINISTRATIONConfiguration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets Copy To...Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product GroupsCurrency Group Default Group G10 Create GroupSelect Member for \"G10\"Available Currencies HKD PLN SGD ZAR AED AFN ALLSelected Currencies AUD CAD CHF EUR GBP JPY NOK NZDCreate Change Request Discard All Changes SaveFigure 11 Bank Basket: Configured Currency Group Note: The same currency can be added to many different groups. The system does not restrict the creation of groups with overlapping sets of currencies. #### 3.1.2 Defining Currency Couple Groups Currency Couple Groups allow the creation of \"buckets\" of currency pairs. Once created, a Currency Couple Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades etc; (2) Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area; and also (3) Order Spot and Forwards found in the Orders Bank Basket area. | | | | | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ $\\Diamond$ AA $\\Box$ $\\Box$ X | |----------------------------------------------------------------|--------------------------------------------------------------|--------------------", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "Currencies may be added or removed from the default group.", "Entity": ["Currencies", "default group"]}, {"Event": "A currency is highlighted with a single-click.", "Entity": ["A currency", "single-click"]}, {"Event": "This activates the single arrow.", "Entity": ["This", "single arrow"]}, {"Event": "Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.", "Entity": ["single arrow", "desired currency", "Available", "Selected"]}, {"Event": "All currencies can be moved in either direction by using the double arrows.", "Entity": ["All currencies", "double arrows"]}, {"Event": "Click Create Group.", "Entity": ["Create Group"]}, {"Event": "Type the desired name.", "Entity": ["desired name"]}, {"Event": "Click Create Group again.", "Entity": ["Create Group"]}, {"Event": "Click Save.", "Entity": ["Save"]}, {"Event": "Provide a name for a new group.", "Entity": ["name", "new group"]}, {"Event": "To view the currencies configured for the group, click on the Currency Group name.", "Entity": ["currencies", "group", "Currency Group name"]}, {"Event": "The same currency can be added to many different groups.", "Entity": ["same currency", "many different groups"]}, {"Event": "The system does not restrict the creation of groups with overlapping sets of currencies.", "Entity": ["system", "creation of groups", "overlapping sets of currencies"]}, {"Event": "Currency Couple Groups allow the creation of \"buckets\" of currency pairs.", "Entity": ["Currency Couple Groups", "buckets of currency pairs"]}, {"Event": "Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades; Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area; and also Order Spot and Forwards found in the Orders Bank Basket area.", "Entity": ["Currency Couple Group", "rules", "FX products", "RFS FX Bank Baskets area", "FX Spot", "Forwards", "Swaps", "NDF", "NDS", "Options", "Block Trades", "Energy Asian and Bullet Swaps", "RFS Commodity Bank Baskets area", "Order Spot", "Forwards", "Orders Bank Basket area"]}], "event_relation_dict": [{"Head": "A currency is highlighted with a single-click.", "Relation": "as a result", "Tail": "The single arrow is activated."}, {"Head": "The single arrow is activated.", "Relation": "before", "Tail": "Clicking the single arrow moves the desired currency."}, {"Head": "A user clicks Create Group.", "Relation": "before", "Tail": "A user types the desired name."}, {"Head": "A user types the desired name.", "Relation": "before", "Tail": "A user clicks Create Group again."}, {"Head": "A user clicks Create Group again.", "Relation": "before", "Tail": "A user clicks Save."}, {"Head": "A user clicks on the Currency Group name.", "Relation": "as a result", "Tail": "The currencies configured for the group are viewed."}, {"Head": "A Currency Couple Group is created.", "Relation": "as a result", "Tail": "The Currency Couple Group can be used to simplify rules."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"Currencies may be added or removed from the default group.\", \"Entity\": [\"Currencies\", \"default group\"]}, {\"Event\": \"A currency is highlighted with a single-click.\", \"Entity\": [\"A currency\", \"single-click\"]}, {\"Event\": \"This activates the single arrow.\", \"Entity\": [\"This\", \"single arrow\"]}, {\"Event\": \"Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.\", \"Entity\": [\"single arrow\", \"desired currency\", \"Available\", \"Selected\"]}, {\"Event\": \"All currencies can be moved in either direction by using the double arrows.\", \"Entity\": [\"All currencies\", \"double arrows\"]}, {\"Event\": \"Click Create Group.\", \"Entity\": [\"Create Group\"]}, {\"Event\": \"Type the desired name.\", \"Entity\": [\"desired name\"]}, {\"Event\": \"Click Create Group again.\", \"Entity\": [\"Create Group\"]}, {\"Event\": \"Click Save.\", \"Entity\": [\"Save\"]}, {\"Event\": \"Provide a name for a new group.\", \"Entity\": [\"name\", \"new group\"]}, {\"Event\": \"To view the currencies configured for the group, click on the Currency Group name.\", \"Entity\": [\"currencies\", \"group\", \"Currency Group name\"]}, {\"Event\": \"The same currency can be added to many different groups.\", \"Entity\": [\"same currency\", \"many different groups\"]}, {\"Event\": \"The system does not restrict the creation of groups with overlapping sets of currencies.\", \"Entity\": [\"system\", \"creation of groups\", \"overlapping sets of currencies\"]}, {\"Event\": \"Currency Couple Groups allow the creation of \\\"buckets\\\" of currency pairs.\", \"Entity\": [\"Currency Couple Groups\", \"buckets of currency pairs\"]}, {\"Event\": \"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades; Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area; and also Order Spot and Forwards found in the Orders Bank Basket area.\", \"Entity\": [\"Currency Couple Group\", \"rules\", \"FX products\", \"RFS FX Bank Baskets area\", \"FX Spot\", \"Forwards\", \"Swaps\", \"NDF\", \"NDS\", \"Options\", \"Block Trades\", \"Energy Asian and Bullet Swaps\", \"RFS Commodity Bank Baskets area\", \"Order Spot\", \"Forwards\", \"Orders Bank Basket area\"]}]", "output_stage_three": "[{\"Head\": \"A currency is highlighted with a single-click.\", \"Relation\": \"as a result\", \"Tail\": \"The single arrow is activated.\"}, {\"Head\": \"The single arrow is activated.\", \"Relation\": \"before\", \"Tail\": \"Clicking the single arrow moves the desired currency.\"}, {\"Head\": \"A user clicks Create Group.\", \"Relation\": \"before\", \"Tail\": \"A user types the desired name.\"}, {\"Head\": \"A user types the desired name.\", \"Relation\": \"before\", \"Tail\": \"A user clicks Create Group again.\"}, {\"Head\": \"A user clicks Create Group again.\", \"Relation\": \"before\", \"Tail\": \"A user clicks Save.\"}, {\"Head\": \"A user clicks on the Currency Group name.\", \"Relation\": \"as a result\", \"Tail\": \"The currencies configured for the group are viewed.\"}, {\"Head\": \"A Currency Couple Group is created.\", \"Relation\": \"as a result\", \"Tail\": \"The Currency Couple Group can be used to simplify rules.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 335, "time": 1.0}, "usage_stage_three": {"completion_tokens": 142, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "-----------------------------|----------------------------|-------------------------------------------------------------------------------------------| | RFS REQUESTER | <b>DEAL TRACKING</b><br><b>BRIDGE ADMINISTRATION</b> | $+$ | | | | | | | | | | Q ※ 上 三<br>侖 | <b>Configuration Groups</b><br>RFS Bank Baskets<br>$\\langle$ | Orders Bank Baskets SEP Bank Baskets<br>Copy To | $\\delta$ | $\\mathcal{A} \\cap \\mathcal{A} \\equiv \\mathcal{A}$ | | △ 盒 360T.ALIAS | Currency Groups<br><b>Currency Couple Groups</b> | FX Time Period Groups<br>MM Time Period Groups | Product Groups | | | €<br><sup> 360T.ALIAS.Company 1</sup> | | | | | | 查 360T.ALIAS.Company 2 | | <b>Currency Couple Group</b> | | | | ₿<br>盒 360T.ALIAS.Company 3 | | Default Group | | al前 | | 盒 360T ALIAS Company 4<br>$\\sqrt{2}$<br>盒 360T.ALIAS.Company 5 | <b>USD v G10</b> | | | al fi | | 盒 360T.ALIAS.Company 6 | | | | Create Group | | ₹ | | | | | | $\\overline{\\mathcal{L}}$ | | | | | | | | | | | | 呢 | | | | | | | | | | <b>DX</b><br>$ +$ | | | | Select Member for \"USD v G10\" | | | | | | <b>Base Currency</b><br><b>Quote Currency</b> | | | | | <b>USD</b> | <b>EUR</b> | | <b>シ</b> 童 | | | USD | JPY | | ジョ | | | USD | GBP | | ショ | | | <b>USD</b> | CHF | | ショ | | | USD | <b>AUD</b> | | $\\bar{z}$<br>市 | | | <b>USD</b> | <b>NZD</b> | | 3/ 自 | | | USD | CAD | | 5/ B | | | <b>USD</b> | SEK | | v)<br>'n | | | | | <b>Add Currency Couple</b> | | | | | | | | | | !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | | | Discard All Changes<br>Save | | $\\frac{1}{\\alpha}$ | 360T ALIAS X | | | | | 360TAS.Treasurer1, 360T.ALIAS // INT | | <b>ELECTE</b> | | Fri, 06: Jul 2018, 08:27:08 GMT // Connected [FFM] . | Figure 12 Bank Basket: Currency Couple Groups The Default Group contains all currency pairs denoted as \\*\\*\\* / \\*\\*\\* for the base and quote currency, respectively. Currency Couple Groups can be created, renamed and removed using the standard Configuration Group icons. Within a group the user can add currency pairs: Click the Add Currency Couple button. Choosing the ISO code in the drop down list or type the desired currency. Confirm the selection by clicking the green check mark . Click Save. Image /page/12/Picture/0 description: { \"image\\_description\": \"The image contains text that reads 'User Guide 360T Bank Baskets Configuration'.\" } Image /page/12/Picture/1 description: { \"image\\_description\": \"Here are the bounding box detections: [{\\\"box\\_2d\\\": [856, 881, 890, 957], \\\"label\\\": \\\"Save\\\"}, {\\\"box\\_2d\\\": [766, 392, 957, 516], \\\"label\\\": \\\"dropdown\\\"}]\" } Figure 13 Bank Basket: Add Currency Couple #### 3.1.3 Defining FX Time Period Groups Products with varying maturities or tenors may be configured into maturity ranges using the FX Time Period Groups. Once created, an FX Time Period Group can be used to simplify rules for (1) FX products found in the RFS FX Bank Baskets area including: Forwards, Swaps, ", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The Default Group contains all currency pairs.", "Entity": ["Default Group", "currency pairs", "base currency", "quote currency"]}, {"Event": "Currency Couple Groups can be created.", "Entity": ["Currency Couple Groups", "Configuration Group icons"]}, {"Event": "Currency Couple Groups can be renamed.", "Entity": ["Currency Couple Groups", "Configuration Group icons"]}, {"Event": "Currency Couple Groups can be removed.", "Entity": ["Currency Couple Groups", "Configuration Group icons"]}, {"Event": "A user can add currency pairs within a group.", "Entity": ["user", "group", "currency pairs"]}, {"Event": "The Add Currency Couple button is clicked.", "Entity": ["Add Currency Couple button"]}, {"Event": "The ISO code is chosen in the drop down list.", "Entity": ["ISO code", "drop down list"]}, {"Event": "The desired currency is typed.", "Entity": ["desired currency"]}, {"Event": "The selection is confirmed by clicking the green check mark.", "Entity": ["selection", "green check mark"]}, {"Event": "Save is clicked.", "Entity": ["Save"]}, {"Event": "Products with varying maturities or tenors may be configured into maturity ranges.", "Entity": ["Products", "maturities", "tenors", "maturity ranges", "FX Time Period Groups"]}, {"Event": "An FX Time Period Group can simplify rules for FX products.", "Entity": ["FX Time Period Group", "rules", "FX products", "RFS FX Bank Baskets area", "Forwards", "Swaps"]}], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The Default Group contains all currency pairs.\", \"Entity\": [\"Default Group\", \"currency pairs\", \"base currency\", \"quote currency\"]}, {\"Event\": \"Currency Couple Groups can be created.\", \"Entity\": [\"Currency Couple Groups\", \"Configuration Group icons\"]}, {\"Event\": \"Currency Couple Groups can be renamed.\", \"Entity\": [\"Currency Couple Groups\", \"Configuration Group icons\"]}, {\"Event\": \"Currency Couple Groups can be removed.\", \"Entity\": [\"Currency Couple Groups\", \"Configuration Group icons\"]}, {\"Event\": \"A user can add currency pairs within a group.\", \"Entity\": [\"user\", \"group\", \"currency pairs\"]}, {\"Event\": \"The Add Currency Couple button is clicked.\", \"Entity\": [\"Add Currency Couple button\"]}, {\"Event\": \"The ISO code is chosen in the drop down list.\", \"Entity\": [\"ISO code\", \"drop down list\"]}, {\"Event\": \"The desired currency is typed.\", \"Entity\": [\"desired currency\"]}, {\"Event\": \"The selection is confirmed by clicking the green check mark.\", \"Entity\": [\"selection\", \"green check mark\"]}, {\"Event\": \"Save is clicked.\", \"Entity\": [\"Save\"]}, {\"Event\": \"Products with varying maturities or tenors may be configured into maturity ranges.\", \"Entity\": [\"Products\", \"maturities\", \"tenors\", \"maturity ranges\", \"FX Time Period Groups\"]}, {\"Event\": \"An FX Time Period Group can simplify rules for FX products.\", \"Entity\": [\"FX Time Period Group\", \"rules\", \"FX products\", \"RFS FX Bank Baskets area\", \"Forwards\", \"Swaps\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 213, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "NDF, NDS, Options and Block Trades etc. and (2) Order Spot and Forwards found in the Orders Bank Basket area. Note: These groups are not relevant for FX Spot and Multi-leg Swaps. The Default Group contains all possible maturities denoted as TODAY / UNLIMITED. The values of the Default Group can be modified, but the group cannot be deleted or renamed. FX Time Period Groups can be created, renamed and removed using the standard Configuration Group icons. FX Time Periods may be added to each group: Click the Add FX Time Period button. Choose the desired time period. Confirm the selection by clicking the green check mark . Click Save. Image /page/13/Picture/0 description: { \"image\\_description\": \"The image contains a user interface for configuring 360T Bank Baskets. It shows options for setting up FX Time Period Groups, including selecting time periods from 'Today' to '3 Months'. The interface includes buttons for creating groups, adding time periods, discarding changes, and saving settings.\" } Figure 14 Bank Basket: Add FX Time Period The ability to add discontinuous tenor ranges is possible. For example, two separate periods may be added for TODAY / 1 WEEK and 1 MONTH / 6 MONTHS. Tenors are defined as a range of maturities, with both start and end values included. The same tenors may be used in various groups in order to be used for different sets of rules. #### 3.1.4 Defining MM Time Period Groups MM products with varying maturities or tenors may be configured into maturity ranges using the MM Time Period Groups. Once created, an MM Time Period Group can be used to simplify rules for interest rate products like Loan, Deposit, Interest Rate Swap, FRA, CapFloor and Tri Party Repo found in the RFS MM Bank Baskets area. The Default Group contains all possible maturities denoted as OVERNIGHT / UNLIMITED. The values of the Default Group can be modified, but the group cannot be deleted or renamed. MM Time Period Groups can be created, renamed and removed using the standard Configuration Group icons. MM Time Periods may be added to each group: Clicking the Add MM Time Period button. Select the desired time period. Confirm the selection by clicking the green check mark . Click Save. Image /page/14/Picture/0 description: { \"image\\_description\": \"The image shows a user interface for configuring bank baskets in 360T, a Deutsche Börse Group platform. The interface includes options for managing MM Time Period Groups, selecting members for a default group, and adding MM Time Periods. The user is currently selecting a time period from the 'From' dropdown menu, with 'OVERNIGHT' highlighted. The 'To' field is set to 'UNLIMITED'. There are also options to create change requests, discard changes, and save the configuration.\" } Figure 15 Bank Basket: Add MM Time Period The ability to add discontinuous tenor ranges is possible. For example, two separate p", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [{"Head": "The Add FX Time Period button is clicked.", "Relation": "before", "Tail": "The desired time period is chosen."}, {"Head": "The desired time period is chosen.", "Relation": "before", "Tail": "The selection is confirmed by clicking the green check mark."}, {"Head": "The selection is confirmed by clicking the green check mark.", "Relation": "before", "Tail": "The Save button is clicked."}, {"Head": "The Add MM Time Period button is clicked.", "Relation": "before", "Tail": "The desired time period is selected."}, {"Head": "The desired time period is selected.", "Relation": "before", "Tail": "The selection is confirmed by clicking the green check mark."}, {"Head": "An MM Time Period Group is created.", "Relation": "as a result", "Tail": "Rules for interest rate products can be simplified."}, {"Head": "The same tenors are used in various groups.", "Relation": "because", "Tail": "They are used for different sets of rules."}], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[{\"Head\": \"The Add FX Time Period button is clicked.\", \"Relation\": \"before\", \"Tail\": \"The desired time period is chosen.\"}, {\"Head\": \"The desired time period is chosen.\", \"Relation\": \"before\", \"Tail\": \"The selection is confirmed by clicking the green check mark.\"}, {\"Head\": \"The selection is confirmed by clicking the green check mark.\", \"Relation\": \"before\", \"Tail\": \"The Save button is clicked.\"}, {\"Head\": \"The Add MM Time Period button is clicked.\", \"Relation\": \"before\", \"Tail\": \"The desired time period is selected.\"}, {\"Head\": \"The desired time period is selected.\", \"Relation\": \"before\", \"Tail\": \"The selection is confirmed by clicking the green check mark.\"}, {\"Head\": \"An MM Time Period Group is created.\", \"Relation\": \"as a result\", \"Tail\": \"Rules for interest rate products can be simplified.\"}, {\"Head\": \"The same tenors are used in various groups.\", \"Relation\": \"because\", \"Tail\": \"They are used for different sets of rules.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 0, "time": 0}, "usage_stage_three": {"completion_tokens": 175, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "eriods may be added for OVERNIGHT / 1 WEEK and 1 MONTH / 6 MONTHS. Tenors are defined as a range of maturities, with both start and end values included. The same tenors may be used in various groups in order to be used for different sets of rules. #### 3.1.5 Defining Product Groups Product Groups are intended to allow the classification of product types into customized groups. This allows setting one single rule for each group of products rather than many rules for individual product types. Image /page/15/Picture/0 description: { \"image\\_description\": \"The image shows a user interface for configuring 360T Bank Baskets. It includes options for RFS Requester, Deal Tracking, and Bridge Administration. The interface allows users to manage currency groups, FX time period groups, MM time period groups, and product groups. A section titled \\\"Select Member for 'FX Spot and Forward'\\\" is visible, with options for available and selected product types.\" } Figure 16 Bank Basket: Product Groups Products can be added or removed from the group without editing the rules themselves. Once created, a product group can be used to simplify rule creation for all relevant product types found in the RFS, Orders and SEP Bank Basket areas. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Please, provide a name for a new group | | MM products | |--|-------------| |--|-------------| | | Cancel | Create Group | |--|--------|--------------| |--|--------|--------------| Figure 17 Bank Basket: Product Groups Create Group A default group exists which includes all products. This group cannot be removed or renamed. However, the product types in the group can be altered. Note: The Default Group contains all product types across RFS, Orders and SEP. However, only the relevant products for RFS, Orders or SEP will apply when a rule utilizes the default Product Group. ## 3.2 Provider Groups and Blocked Providers Each of the Bank Basket areas (RFS, Orders and SEP) provides the possibility to create individual Provider Groups and to also temporarily block providers. Image /page/16/Picture/0 description: { \"image\\_description\": \"The image shows a green button with the text \\\"360T\\\" on it. The button has a white outline and a white background. The text is in a stylized font.\" } DEUTSCHE BÖRSE GROUP User Guide 360T Bank Baskets Configuration | RFS REQUESTER | DEAL TRACKING | BRIDGE ADMINISTRATION | + | |---------------|---------------|-----------------------|---| |---------------|---------------|-----------------------|---| | Preferences | Administration | Help | A A | X | |-------------|----------------|------|-----|---| |-------------|----------------|------|-----|---| | | Q | | | |--|----------------------|--|--| | | 360T ALIAS | | | | | 360T ALIAS Company 1 | | | | | 360T ALIAS Company 2 | | | | | 360T ALIAS Company 3 ", "entity_relation_dict": [{"Head": "Tenors", "Relation": "are defined as", "Tail": "range of maturities"}, {"Head": "Tenors", "Relation": "include", "Tail": "start and end values"}, {"Head": "Tenors", "Relation": "are used in", "Tail": "various groups"}, {"Head": "Product Groups", "Relation": "classify", "Tail": "product types"}, {"Head": "Product Groups", "Relation": "enable setting", "Tail": "single rule"}, {"Head": "Products", "Relation": "can be managed within", "Tail": "product group"}, {"Head": "Product group", "Relation": "simplifies", "Tail": "rule creation"}, {"Head": "Rule creation", "Relation": "applies to", "Tail": "relevant product types"}, {"Head": "Default group", "Relation": "includes", "Tail": "all products"}, {"Head": "Default group", "Relation": "cannot be", "Tail": "removed"}, {"Head": "Default group", "Relation": "cannot be", "Tail": "renamed"}, {"Head": "Product types", "Relation": "can be altered in", "Tail": "Default Group"}, {"Head": "Relevant products", "Relation": "apply with", "Tail": "default Product Group"}, {"Head": "Bank Basket areas", "Relation": "allow creation of", "Tail": "Provider Groups"}, {"Head": "Bank Basket areas", "Relation": "allow blocking of", "Tail": "providers"}, {"Head": "User interface", "Relation": "includes", "Tail": "RFS Requester option"}, {"Head": "User interface", "Relation": "includes", "Tail": "Deal Tracking option"}, {"Head": "User interface", "Relation": "includes", "Tail": "Bridge Administration option"}, {"Head": "User interface", "Relation": "allows management of", "Tail": "currency groups"}, {"Head": "User interface", "Relation": "allows management of", "Tail": "FX time period groups"}, {"Head": "User interface", "Relation": "allows management of", "Tail": "MM time period groups"}, {"Head": "User interface", "Relation": "allows management of", "Tail": "product groups"}, {"Head": "\"Select Member for 'FX Spot and Forward'\" section", "Relation": "contains", "Tail": "available product types"}, {"Head": "\"Select Member for 'FX Spot and Forward'\" section", "Relation": "contains", "Tail": "selected product types"}], "event_entity_relation_dict": [{"Event": "Periods can be added for specific durations.", "Entity": ["periods", "OVERNIGHT", "1 WEEK", "1 MONTH", "6 MONTHS"]}, {"Event": "Tenors are defined as a range of maturities.", "Entity": ["Tenors", "range of maturities", "start values", "end values"]}, {"Event": "The same tenors can be used in various groups for different sets of rules.", "Entity": ["tenors", "groups", "sets of rules"]}, {"Event": "Product Groups are intended to classify product types into customized groups.", "Entity": ["Product Groups", "product types", "customized groups"]}, {"Event": "This allows setting a single rule for each group of products.", "Entity": ["rule", "group of products", "product types"]}, {"Event": "The image displays a user interface for configuring 360T Bank Baskets.", "Entity": ["image", "user interface", "360T Bank Baskets"]}, {"Event": "The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.", "Entity": ["user interface", "RFS Requester", "Deal Tracking", "Bridge Administration"]}, {"Event": "The interface enables users to manage various groups.", "Entity": ["interface", "users", "currency groups", "FX time period groups", "MM time period groups", "product groups"]}, {"Event": "A section titled \"Select Member for 'FX Spot and Forward'\" is visible.", "Entity": ["section", "FX Spot and Forward", "product types"]}, {"Event": "Products can be added or removed from the group.", "Entity": ["Products", "group", "rules"]}, {"Event": "A product group can simplify rule creation for relevant product types.", "Entity": ["product group", "rule creation", "product types", "RFS", "Orders", "SEP Bank Basket areas"]}, {"Event": "A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.", "Entity": ["new group", "Create Group", "name", "Save"]}, {"Event": "A default group exists that includes all products.", "Entity": ["default group", "products"]}, {"Event": "This group cannot be removed or renamed.", "Entity": ["group"]}, {"Event": "The product types in the group can be altered.", "Entity": ["product types", "group"]}, {"Event": "The Default Group contains all product types across RFS, Orders and SEP.", "Entity": ["Default Group", "product types", "RFS", "Orders", "SEP"]}, {"Event": "Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.", "Entity": ["products", "RFS", "Orders", "SEP", "rule", "default Product Group"]}, {"Event": "Each Bank Basket area allows creating Provider Groups and blocking providers.", "Entity": ["Bank Basket areas", "RFS", "Orders", "SEP", "Provider Groups", "providers"]}, {"Event": "The image displays a green button with \"360T\" text.", "Entity": ["image", "green button", "360T"]}, {"Event": "The button features a white outline and a white background.", "Entity": ["button", "white outline", "white background"]}, {"Event": "The text is presented in a stylized font.", "Entity": ["text", "stylized font"]}], "event_relation_dict": [{"Head": "Product Groups classify product types into customized groups", "Relation": "because", "Tail": "One single rule can be set for each group of products"}, {"Head": "A product group is created", "Relation": "before", "Tail": "A product group can be used to simplify rule creation"}, {"Head": "Click Create Group", "Relation": "before", "Tail": "Type the desired name"}, {"Head": "Type the desired name", "Relation": "before", "Tail": "Click Create Group again"}, {"Head": "Click Create Group again", "Relation": "before", "Tail": "Click Save"}, {"Head": "A default group exists which includes all products", "Relation": "as a result", "Tail": "This default group cannot be removed or renamed"}, {"Head": "A rule utilizes the default Product Group", "Relation": "as a result", "Tail": "Only the relevant products for RFS, Orders or SEP will apply"}], "output_stage_one": "[{\"Head\": \"Tenors\", \"Relation\": \"are defined as\", \"Tail\": \"range of maturities\"}, {\"Head\": \"Tenors\", \"Relation\": \"include\", \"Tail\": \"start and end values\"}, {\"Head\": \"Tenors\", \"Relation\": \"are used in\", \"Tail\": \"various groups\"}, {\"Head\": \"Product Groups\", \"Relation\": \"classify\", \"Tail\": \"product types\"}, {\"Head\": \"Product Groups\", \"Relation\": \"enable setting\", \"Tail\": \"single rule\"}, {\"Head\": \"Products\", \"Relation\": \"can be managed within\", \"Tail\": \"product group\"}, {\"Head\": \"Product group\", \"Relation\": \"simplifies\", \"Tail\": \"rule creation\"}, {\"Head\": \"Rule creation\", \"Relation\": \"applies to\", \"Tail\": \"relevant product types\"}, {\"Head\": \"Default group\", \"Relation\": \"includes\", \"Tail\": \"all products\"}, {\"Head\": \"Default group\", \"Relation\": \"cannot be\", \"Tail\": \"removed\"}, {\"Head\": \"Default group\", \"Relation\": \"cannot be\", \"Tail\": \"renamed\"}, {\"Head\": \"Product types\", \"Relation\": \"can be altered in\", \"Tail\": \"Default Group\"}, {\"Head\": \"Relevant products\", \"Relation\": \"apply with\", \"Tail\": \"default Product Group\"}, {\"Head\": \"Bank Basket areas\", \"Relation\": \"allow creation of\", \"Tail\": \"Provider Groups\"}, {\"Head\": \"Bank Basket areas\", \"Relation\": \"allow blocking of\", \"Tail\": \"providers\"}, {\"Head\": \"User interface\", \"Relation\": \"includes\", \"Tail\": \"RFS Requester option\"}, {\"Head\": \"User interface\", \"Relation\": \"includes\", \"Tail\": \"Deal Tracking option\"}, {\"Head\": \"User interface\", \"Relation\": \"includes\", \"Tail\": \"Bridge Administration option\"}, {\"Head\": \"User interface\", \"Relation\": \"allows management of\", \"Tail\": \"currency groups\"}, {\"Head\": \"User interface\", \"Relation\": \"allows management of\", \"Tail\": \"FX time period groups\"}, {\"Head\": \"User interface\", \"Relation\": \"allows management of\", \"Tail\": \"MM time period groups\"}, {\"Head\": \"User interface\", \"Relation\": \"allows management of\", \"Tail\": \"product groups\"}, {\"Head\": \"\\\"Select Member for 'FX Spot and Forward'\\\" section\", \"Relation\": \"contains\", \"Tail\": \"available product types\"}, {\"Head\": \"\\\"Select Member for 'FX Spot and Forward'\\\" section\", \"Relation\": \"contains\", \"Tail\": \"selected product types\"}]", "output_stage_two": "[{\"Event\": \"Periods can be added for specific durations.\", \"Entity\": [\"periods\", \"OVERNIGHT\", \"1 WEEK\", \"1 MONTH\", \"6 MONTHS\"]}, {\"Event\": \"Tenors are defined as a range of maturities.\", \"Entity\": [\"Tenors\", \"range of maturities\", \"start values\", \"end values\"]}, {\"Event\": \"The same tenors can be used in various groups for different sets of rules.\", \"Entity\": [\"tenors\", \"groups\", \"sets of rules\"]}, {\"Event\": \"Product Groups are intended to classify product types into customized groups.\", \"Entity\": [\"Product Groups\", \"product types\", \"customized groups\"]}, {\"Event\": \"This allows setting a single rule for each group of products.\", \"Entity\": [\"rule\", \"group of products\", \"product types\"]}, {\"Event\": \"The image displays a user interface for configuring 360T Bank Baskets.\", \"Entity\": [\"image\", \"user interface\", \"360T Bank Baskets\"]}, {\"Event\": \"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.\", \"Entity\": [\"user interface\", \"RFS Requester\", \"Deal Tracking\", \"Bridge Administration\"]}, {\"Event\": \"The interface enables users to manage various groups.\", \"Entity\": [\"interface\", \"users\", \"currency groups\", \"FX time period groups\", \"MM time period groups\", \"product groups\"]}, {\"Event\": \"A section titled \\\"Select Member for 'FX Spot and Forward'\\\" is visible.\", \"Entity\": [\"section\", \"FX Spot and Forward\", \"product types\"]}, {\"Event\": \"Products can be added or removed from the group.\", \"Entity\": [\"Products\", \"group\", \"rules\"]}, {\"Event\": \"A product group can simplify rule creation for relevant product types.\", \"Entity\": [\"product group\", \"rule creation\", \"product types\", \"RFS\", \"Orders\", \"SEP Bank Basket areas\"]}, {\"Event\": \"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.\", \"Entity\": [\"new group\", \"Create Group\", \"name\", \"Save\"]}, {\"Event\": \"A default group exists that includes all products.\", \"Entity\": [\"default group\", \"products\"]}, {\"Event\": \"This group cannot be removed or renamed.\", \"Entity\": [\"group\"]}, {\"Event\": \"The product types in the group can be altered.\", \"Entity\": [\"product types\", \"group\"]}, {\"Event\": \"The Default Group contains all product types across RFS, Orders and SEP.\", \"Entity\": [\"Default Group\", \"product types\", \"RFS\", \"Orders\", \"SEP\"]}, {\"Event\": \"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.\", \"Entity\": [\"products\", \"RFS\", \"Orders\", \"SEP\", \"rule\", \"default Product Group\"]}, {\"Event\": \"Each Bank Basket area allows creating Provider Groups and blocking providers.\", \"Entity\": [\"Bank Basket areas\", \"RFS\", \"Orders\", \"SEP\", \"Provider Groups\", \"providers\"]}, {\"Event\": \"The image displays a green button with \\\"360T\\\" text.\", \"Entity\": [\"image\", \"green button\", \"360T\"]}, {\"Event\": \"The button features a white outline and a white background.\", \"Entity\": [\"button\", \"white outline\", \"white background\"]}, {\"Event\": \"The text is presented in a stylized font.\", \"Entity\": [\"text\", \"stylized font\"]}]", "output_stage_three": "[{\"Head\": \"Product Groups classify product types into customized groups\", \"Relation\": \"because\", \"Tail\": \"One single rule can be set for each group of products\"}, {\"Head\": \"A product group is created\", \"Relation\": \"before\", \"Tail\": \"A product group can be used to simplify rule creation\"}, {\"Head\": \"Click Create Group\", \"Relation\": \"before\", \"Tail\": \"Type the desired name\"}, {\"Head\": \"Type the desired name\", \"Relation\": \"before\", \"Tail\": \"Click Create Group again\"}, {\"Head\": \"Click Create Group again\", \"Relation\": \"before\", \"Tail\": \"Click Save\"}, {\"Head\": \"A default group exists which includes all products\", \"Relation\": \"as a result\", \"Tail\": \"This default group cannot be removed or renamed\"}, {\"Head\": \"A rule utilizes the default Product Group\", \"Relation\": \"as a result\", \"Tail\": \"Only the relevant products for RFS, Orders or SEP will apply\"}]", "usage_stage_one": {"completion_tokens": 292, "time": 1.0}, "usage_stage_two": {"completion_tokens": 446, "time": 1.0}, "usage_stage_three": {"completion_tokens": 139, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "| | | | | 360T ALIAS Company 4 | | | | | 360T ALIAS Company 5 | | | | | 360T.ALIAS.Company 6 | | | | Configuration Groups | RFS Bank Baskets | Orders Bank Baskets | SEP Bank Baskets | |----------------------|------------------|---------------------|------------------| |----------------------|------------------|---------------------|------------------| | RFS Provider Groups | Blocked RFS Providers | RFS FX Bank Baskets | RFS MM Bank Baskets | RFS Commodity Bank Baskets | RFS Cross Currency Netting Bank Baskets | |---------------------|-----------------------|---------------------|---------------------|----------------------------|-----------------------------------------| |---------------------|-----------------------|---------------------|---------------------|----------------------------|-----------------------------------------| | Available Providers | Blocked Providers | |---------------------|-------------------| | Barclays BARX.DEMO | 360TBANK.TEST | | BOAL DEMO | | | CITIBANK DEMO | | | COBA DEMO | | | RBC DEMO | | | RBS.LND DEMO | | | SEB FRA DEMO | | | SOCGEN.LND.DEMO | | | Create Change Request | Discard All Changes | Save | |-----------------------|---------------------|------| |-----------------------|---------------------|------| | 360T ALIAS * X | 360T ALIAS Company 1 * X | |----------------|--------------------------| |----------------|--------------------------| | 360TAS Treasurer1, 360T ALIAS // INT | Fri, 06. Jul 2018, 11:03:01 GMT // Connected [FFM] | |--------------------------------------|----------------------------------------------------| |--------------------------------------|----------------------------------------------------| Figure 18 Bank Basket: Blocked RFS Providers The Provider Groups themselves can be edited (values added or removed) without changing a set of rules based on those groups. Providers may also be temporarily blocked from particular request types without affecting the configured Provider Group. A Blocked Provider will remain in a Provider Group but will appear with a \"blocked\" symbol. If a Provider should be removed completely, the relationship should be rejected using the Counterpart Relationship Management tool. In this case, please refer to the relevant user guide. | | | | Preferences Administration Help AA X | |--|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "Provider Groups can be edited.", "Entity": ["Provider Groups", "values", "rules"]}, {"Event": "Providers can be temporarily blocked from particular request types.", "Entity": ["Providers", "request types", "Provider Group"]}, {"Event": "A Blocked Provider will remain in a Provider Group and appear with a \"blocked\" symbol.", "Entity": ["Blocked Provider", "Provider Group", "\"blocked\" symbol"]}, {"Event": "A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.", "Entity": ["Provider", "relationship", "Counterpart Relationship Management tool"]}, {"Event": "Users should refer to the relevant user guide.", "Entity": ["user guide"]}], "event_relation_dict": [{"Head": "Providers are temporarily blocked from particular request types.", "Relation": "as a result", "Tail": "A Blocked Provider will remain in a Provider Group but will appear with a \"blocked\" symbol."}, {"Head": "A Provider should be removed completely.", "Relation": "as a result", "Tail": "The relationship should be rejected using the Counterpart Relationship Management tool."}, {"Head": "The relationship is rejected using the Counterpart Relationship Management tool.", "Relation": "as a result", "Tail": "Please refer to the relevant user guide."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"Provider Groups can be edited.\", \"Entity\": [\"Provider Groups\", \"values\", \"rules\"]}, {\"Event\": \"Providers can be temporarily blocked from particular request types.\", \"Entity\": [\"Providers\", \"request types\", \"Provider Group\"]}, {\"Event\": \"A Blocked Provider will remain in a Provider Group and appear with a \\\"blocked\\\" symbol.\", \"Entity\": [\"Blocked Provider\", \"Provider Group\", \"\\\"blocked\\\" symbol\"]}, {\"Event\": \"A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.\", \"Entity\": [\"Provider\", \"relationship\", \"Counterpart Relationship Management tool\"]}, {\"Event\": \"Users should refer to the relevant user guide.\", \"Entity\": [\"user guide\"]}]", "output_stage_three": "[{\"Head\": \"Providers are temporarily blocked from particular request types.\", \"Relation\": \"as a result\", \"Tail\": \"A Blocked Provider will remain in a Provider Group but will appear with a \\\"blocked\\\" symbol.\"}, {\"Head\": \"A Provider should be removed completely.\", \"Relation\": \"as a result\", \"Tail\": \"The relationship should be rejected using the Counterpart Relationship Management tool.\"}, {\"Head\": \"The relationship is rejected using the Counterpart Relationship Management tool.\", \"Relation\": \"as a result\", \"Tail\": \"Please refer to the relevant user guide.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 100, "time": 1.0}, "usage_stage_three": {"completion_tokens": 86, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "----------------|-----------------------------------------------------------------------------------------------------------------------| | | RFS REQUESTER DEAL TRACKING | + BRIDGE ADMINISTRATION | | | | Q <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> < 360T.ALIAS | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets <img alt=\"icon\" src=\"icon_image\"/> RFS Provider Groups Blocked RFS Providers RFS FX Bank Baskets RFS MM Bank Baskets RFS Commodity Bank Baskets RFS Cross Currency Netting Bank Baskets | | | | 360T.ALIAS.Company 1 360T.ALIAS.Company 2 360T.ALIAS.Company 3 360T.ALIAS.Company 4 360T.ALIAS.Company 5 360T.ALIAS.Company 6 | Provider Group Default Group Nordic Banks AUD Banks | <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> <img alt=\"icon\" src=\"icon_image\"/> Create Group | | | | Select Member for \"Nordic Banks\" Available Providers Selected Providers Barclays BARX.DEMO BOAL DEMO CITIBANK DEMO COBA DEMO RBS.LND.DEMO SOCGEN.LND DEMO TB-HSBC DEMO | - x 360TBANK.TEST RBC.DEMO SEB FRA.DEMO | | | 360TAS Treasurer1, 360T ALIAS // INT | Create Change Request 360T ALIAS x 360T.ALIAS Company 1 x | Discard All Changes Save Fri, 06. Jul 2018, 11:06:37 GMT // Connected [FFM] · | Figure 19 Bank Basket: RFS Provider Groups With the initial activation of the Bank Basket configuration, the Default Group will include all active-relationship providers (where the Counterpart Relationship is \"green-green\"). Note: New Counterparties (newly accepted relationships) will be added to the Default Group. To add a new group: Click Create Group > Type the desired name > Click Create Group again > Click Save. Adding or removing providers is done by moving certain banks from \"Available Providers\" to \"Selected Providers\" using the arrow buttons. ## 3.3 Bank Basket Rules #### 3.3.1 Defining Bank Basket Rules Once all configuration groups are in place, individual rules may be defined using the relevant groups for each Bank Basket area (RFS, Orders and SEP). The workflow to define bank basket rules is very similar across all Bank Basket areas. The relevant Configuration Groups may vary. With the initial activation, all Bank Baskets will contain one single rule denoted with a combination of Any / Default Group for each parameter. To add a new rule: Click Add Rule. Select an option from each of the configuration groups using the drop down menu. Confirm the selection by clicking the green check mark . Click Save. | | | | | | v Preferences v Administration v Help G AA - C X | | |-----------------------------|--------------------------------------------------|------------------------------------------------------------------------|-----------------------------------------|-------------------------------|------------------------------------------------------|-------------------| | | <b>RFS REQUESTER<", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.", "Entity": ["Default Group", "active-relationship providers", "Bank Basket configuration", "Counterpart Relationship"]}, {"Event": "New Counterparties will be added to the Default Group.", "Entity": ["New Counterparties", "Default Group"]}, {"Event": "A user clicks Create Group to add a new group.", "Entity": ["user", "Create Group", "new group"]}, {"Event": "A user types the desired name.", "Entity": ["user", "desired name"]}, {"Event": "A user clicks Create Group again.", "Entity": ["user", "Create Group"]}, {"Event": "A user clicks Save.", "Entity": ["user", "Save"]}, {"Event": "Providers are added or removed by moving banks from \"Available Providers\" to \"Selected Providers\" using arrow buttons.", "Entity": ["providers", "banks", "Available Providers", "Selected Providers", "arrow buttons"]}], "event_relation_dict": [{"Head": "The Default Group will include all active-relationship providers.", "Relation": "at the same time", "Tail": "All Bank Baskets will contain one single rule."}, {"Head": "The Default Group will include all active-relationship providers.", "Relation": "after", "Tail": "New Counterparties will be added to the Default Group."}, {"Head": "All configuration groups are in place.", "Relation": "before", "Tail": "Individual rules may be defined using the relevant groups for each Bank Basket area."}, {"Head": "Click Add Rule.", "Relation": "before", "Tail": "Select an option from each of the configuration groups using the drop down menu."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.\", \"Entity\": [\"Default Group\", \"active-relationship providers\", \"Bank Basket configuration\", \"Counterpart Relationship\"]}, {\"Event\": \"New Counterparties will be added to the Default Group.\", \"Entity\": [\"New Counterparties\", \"Default Group\"]}, {\"Event\": \"A user clicks Create Group to add a new group.\", \"Entity\": [\"user\", \"Create Group\", \"new group\"]}, {\"Event\": \"A user types the desired name.\", \"Entity\": [\"user\", \"desired name\"]}, {\"Event\": \"A user clicks Create Group again.\", \"Entity\": [\"user\", \"Create Group\"]}, {\"Event\": \"A user clicks Save.\", \"Entity\": [\"user\", \"Save\"]}, {\"Event\": \"Providers are added or removed by moving banks from \\\"Available Providers\\\" to \\\"Selected Providers\\\" using arrow buttons.\", \"Entity\": [\"providers\", \"banks\", \"Available Providers\", \"Selected Providers\", \"arrow buttons\"]}]", "output_stage_three": "[{\"Head\": \"The Default Group will include all active-relationship providers.\", \"Relation\": \"at the same time\", \"Tail\": \"All Bank Baskets will contain one single rule.\"}, {\"Head\": \"The Default Group will include all active-relationship providers.\", \"Relation\": \"after\", \"Tail\": \"New Counterparties will be added to the Default Group.\"}, {\"Head\": \"All configuration groups are in place.\", \"Relation\": \"before\", \"Tail\": \"Individual rules may be defined using the relevant groups for each Bank Basket area.\"}, {\"Head\": \"Click Add Rule.\", \"Relation\": \"before\", \"Tail\": \"Select an option from each of the configuration groups using the drop down menu.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 148, "time": 1.0}, "usage_stage_three": {"completion_tokens": 104, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "/b><br><b>DEAL TRACKING</b> | BRIDGE ADMINISTRATION<br>$+$ | | | | | | 合 | Q 卷 1<br>△ 盒 360T.ALIAS | Configuration Groups<br>RFS Bank Baskets | Orders Bank Baskets<br>SEP Bank Baskets | $\\partial \\mathcal{E}_k$ | | $\\Omega \\cap \\Xi$ | | $\\mathcal{L}_{\\mathcal{F}}$ | 盒 360T.ALIAS.Company 1 | Orders Provider Groups<br><b>Blocked Orders Providers</b> | Orders FX Bank Baskets | | | | | ₿ | 盒 360T.ALIAS.Company 2<br>意 360T ALIAS Company 3 | POS Product Type | Currency Couple | <b>Time Period</b> | <b>Bank Basket</b> | | | | 盒 360T.ALIAS.Company 4 | Default Group<br>$21 - 1$ | M/m | Default Group | Preferred Limit Order Providers | M E | | 凾 | 盒 360T.ALIAS.Company 5 | Default Group<br>$-2$ | AUD/*** | Default Group | Preferred Limit Order Providers | | | - | 盒 360T.ALIAS.Company 6 | 3<br>Any | Any | Any | Default Group | | | 同 | | Default Group<br>$\\hat{\\phantom{a}}$ | Default Group<br>$\\checkmark$ | Default Group<br>$\\checkmark$ | Default Group<br>$\\checkmark$ | Mi | | $\\frac{1}{\\sqrt{2}}$ | | Any | | | | Add Rule | | ශී | | Default Group<br>Spot. | | | | | | | | Options<br>Non deliverable<br>Streaming spot<br>Money market<br>Custom | | | | | | $Q$ D $Q$ | | Create Change Request | | | Discard All Changes<br><b>Save</b> | | | | | 360T.ALIAS.Company 1 ' X<br>360T.ALIAS * X | | | | | | | 360TAS.Treasurer1, 360TALIAS: // INT | | <b>SECT</b> | | Fri, 06. Jul 2018, 11:28:21 GMT // Connected [FFM] @ | | Figure 20 Bank Basket: Add Rule For each Configuration Group, the user can either explicitly select one of the previously saved groups, \"Any\" or \"Custom\". The selection of \"Custom\" in each rule parameter allows to define a criteria in that moment. A predefined Configuration group is not needed. For example, if no Currency Couple Group was defined for AUD, but a specific bank basket should be used for all AUD requests, then the rules shown in the figure below can be defined. Image /page/18/Picture/0 description: { \"image\\_description\": \"The image contains the text '360T' in a green, stylized font on a white background. The text appears to be a logo or brand name.\" } User Guide 360T Bank Baskets Configuration | | <b>DEAL TRACKING</b><br><b>RFS Requester</b> | | $+$<br><b>Bridge Administration</b> | | | | | |-----------------------------|--------------------------------------------------------------------|---------------|-------------------------------------------------|------------------------------------------------------------|---------------------|-----------------------------------|--------------| | 合 | 激<br>Q<br>$\\mathbb{Z}$ | | Configuration Groups<br><b>RFS Bank Baskets</b> | Orders Bank Baskets | SEP Bank Baskets | | の心量 | | | △ 盒 360T.ALIAS | | <b>RFS Provider Groups</b> | <b>Blocked RFS Providers</b><br><b>RFS Fx Bank Baskets</b> | RFS Mm Bank Baskets | <b>RFS Commodity Bank Baskets</b> | RFS Cross Cr | | $\\mathcal{L}_{\\mathcal{F}}$ | <", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "The user can explicitly select one of the previously saved groups, \"Any\" or \"Custom\" for each Configuration Group.", "Entity": ["user", "Configuration Group", "previously saved groups", "Any", "Custom"]}, {"Event": "The selection of \"Custom\" in each rule parameter allows to define a criteria in that moment.", "Entity": ["Custom", "rule parameter", "criteria"]}, {"Event": "A predefined Configuration group is not needed.", "Entity": ["predefined Configuration group"]}, {"Event": "Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.", "Entity": ["rules", "figure below", "Currency Couple Group", "AUD", "bank basket", "AUD requests"]}, {"Event": "The image contains the text '360T' in a green, stylized font on a white background.", "Entity": ["image", "text '360T'", "green, stylized font", "white background"]}, {"Event": "The text appears to be a logo or brand name.", "Entity": ["text", "logo", "brand name"]}, {"Event": "A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.", "Entity": ["connection", "Fri, 06. Jul 2018, 11:28:21 GMT", "FFM"]}], "event_relation_dict": [{"Head": "The user selects 'Custom' in a rule parameter.", "Relation": "as a result", "Tail": "A criteria can be defined in that moment."}, {"Head": "A criteria can be defined in that moment.", "Relation": "as a result", "Tail": "A predefined Configuration group is not needed."}, {"Head": "No Currency Couple Group was defined for AUD and a specific bank basket should be used for all AUD requests.", "Relation": "as a result", "Tail": "The rules shown in the figure below can be defined."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"The user can explicitly select one of the previously saved groups, \\\"Any\\\" or \\\"Custom\\\" for each Configuration Group.\", \"Entity\": [\"user\", \"Configuration Group\", \"previously saved groups\", \"Any\", \"Custom\"]}, {\"Event\": \"The selection of \\\"Custom\\\" in each rule parameter allows to define a criteria in that moment.\", \"Entity\": [\"Custom\", \"rule parameter\", \"criteria\"]}, {\"Event\": \"A predefined Configuration group is not needed.\", \"Entity\": [\"predefined Configuration group\"]}, {\"Event\": \"Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.\", \"Entity\": [\"rules\", \"figure below\", \"Currency Couple Group\", \"AUD\", \"bank basket\", \"AUD requests\"]}, {\"Event\": \"The image contains the text '360T' in a green, stylized font on a white background.\", \"Entity\": [\"image\", \"text '360T'\", \"green, stylized font\", \"white background\"]}, {\"Event\": \"The text appears to be a logo or brand name.\", \"Entity\": [\"text\", \"logo\", \"brand name\"]}, {\"Event\": \"A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.\", \"Entity\": [\"connection\", \"Fri, 06. Jul 2018, 11:28:21 GMT\", \"FFM\"]}]", "output_stage_three": "[{\"Head\": \"The user selects 'Custom' in a rule parameter.\", \"Relation\": \"as a result\", \"Tail\": \"A criteria can be defined in that moment.\"}, {\"Head\": \"A criteria can be defined in that moment.\", \"Relation\": \"as a result\", \"Tail\": \"A predefined Configuration group is not needed.\"}, {\"Head\": \"No Currency Couple Group was defined for AUD and a specific bank basket should be used for all AUD requests.\", \"Relation\": \"as a result\", \"Tail\": \"The rules shown in the figure below can be defined.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 201, "time": 1.0}, "usage_stage_three": {"completion_tokens": 87, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "sup> 360T.ALIAS.Company 1</sup> | | | | | | | | $\\sim$ | 360T.ALIAS.Company 2 | POS | Product Type | <b>Currency Couple</b> | <b>Time Period</b> | <b>Bank Basket</b> | | | P | <b>童 360T.ALIAS.Company 3</b><br><sup>1</sup> 360T.ALIAS.Company 4 | ÷ | Forwards and Swaps | Scandies | Default Group | Nordic Banks | ショ | | $\\frac{1}{\\sqrt{2}}$ | <sup> 360T.ALIAS.Company 5</sup> | $\\frac{1}{2}$ | Any | $M+++$ | Any | AUD Banks | ショ | | $-$ | | ÷<br>3 | Any | AUD/*** | Any | AUD Banks | √ □ | | ę | <b>血 360T.ALIAS.Company 6</b> | ÷<br>4 | Any | Any | Any | Default Group | シー | Figure 21 Use Custom selection for Currency couples In the example above, all RFS requests with base currency AUD (Rule 2) or terms currency AUD (Rule 3) will use the AUD-Group bank basket. The tenor can be used in a similar way to determine the banks to which a request will be sent. #### 3.3.2 Order of the Rules The POS (position) column indicates which rules take precedent. For each individual request, the Bank Basket will be chosen based on the rule order 1 … n, where 1 is first. If the request meets the criteria found in Rule 1, this rule will be used to define the Bank Basket. Therefore it is advisable to sort the rules with the most restrictive definition on top. In order to move an existing rule up or down in the list, please select the relevant line with the mouse. Drag and drop the rule into the desired position. | | <b>Configuration Groups</b> | <b>RFS Bank Baskets</b> | <b>Orders Bank Baskets</b> | | SEP Bi | |----------------|-------------------------------|---------------------------------|----------------------------|------------------------|--------| | | <b>Orders Provider Groups</b> | <b>Blocked Orders Providers</b> | | <b>Orders Fx Bank</b> | | | | | | | | | | POS | Product Type | | | <b>Currency Couple</b> | | | 1 | Default Group | | | ***/AUD | | | $\\overline{2}$ | Default Group | | | AUD/*** | | Figure 22 Bank Basket: Order of Rules ## 3.4 Note on Supersonic The possibility to configure currency Bank Baskets for Supersonic is not available yet. Bank Basket selection is configured directly in the Supersonic interface by defining SEP Contributors for each spot currency pair. Therefore, the SEP Bank Baskets are currently only used to block SEP providers if needed. ## 4 BANK BASKETS EVALUATOR TOOL The Bank Baskets Evaluator Tool may assist the user in identifying which rule applies to a particular type of request. The tool can be accessed from the Bridge Administration Homepage. Image /page/19/Picture/0 description: { \"image\\_description\": \"The image shows a screenshot of the 360T Bank Baskets Configuration user interface. The main area of the screen displays the 'Administration Start' page, which includes sections for 'Configurations' and 'Actions'. Under 'Configurations', there are icons for 'Regulatory Data' and 'Bank Baskets'. Under 'Actions', ther", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "RFS requests will use the AUD-Group bank basket based on currency rules.", "Entity": ["RFS requests", "base currency AUD", "Rule 2", "terms currency AUD", "Rule 3", "AUD-Group bank basket"]}, {"Event": "The tenor can determine the banks for sending a request.", "Entity": ["tenor", "banks", "request"]}, {"Event": "The POS column indicates rule precedence.", "Entity": ["POS column", "rules"]}, {"Event": "The Bank Basket is chosen for each request based on rule order.", "Entity": ["individual request", "Bank Basket", "rule order 1", "rule order n"]}, {"Event": "Rule 1 defines the Bank Basket if a request meets its criteria.", "Entity": ["Rule 1", "Bank Basket", "request", "criteria"]}, {"Event": "Rules should be sorted with the most restrictive definition on top.", "Entity": ["rules", "restrictive definition"]}, {"Event": "A user can select a rule line with a mouse to move it.", "Entity": ["existing rule", "list", "mouse"]}, {"Event": "A user can drag and drop a rule into a desired position.", "Entity": ["rule", "desired position"]}, {"Event": "Configuration of currency Bank Baskets for Supersonic is not yet available.", "Entity": ["currency Bank Baskets", "Supersonic"]}, {"Event": "Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.", "Entity": ["Bank Basket selection", "Supersonic interface", "SEP Contributors", "spot currency pair"]}, {"Event": "SEP Bank Baskets are used to block SEP providers.", "Entity": ["SEP Bank Baskets", "SEP providers"]}, {"Event": "The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.", "Entity": ["Bank Baskets Evaluator Tool", "user", "rule", "particular type of request"]}, {"Event": "The tool can be accessed from the Bridge Administration Homepage.", "Entity": ["tool", "Bridge Administration Homepage"]}, {"Event": "The image displays a screenshot of the 360T Bank Baskets Configuration user interface.", "Entity": ["image", "screenshot", "360T Bank Baskets Configuration user interface"]}, {"Event": "The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.", "Entity": ["main area", "screen", "'Administration Start' page", "'Configurations' section", "'Actions' section"]}, {"Event": "Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.", "Entity": ["'Configurations'", "'Regulatory Data' icon", "'Bank Baskets' icon"]}], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"RFS requests will use the AUD-Group bank basket based on currency rules.\", \"Entity\": [\"RFS requests\", \"base currency AUD\", \"Rule 2\", \"terms currency AUD\", \"Rule 3\", \"AUD-Group bank basket\"]}, {\"Event\": \"The tenor can determine the banks for sending a request.\", \"Entity\": [\"tenor\", \"banks\", \"request\"]}, {\"Event\": \"The POS column indicates rule precedence.\", \"Entity\": [\"POS column\", \"rules\"]}, {\"Event\": \"The Bank Basket is chosen for each request based on rule order.\", \"Entity\": [\"individual request\", \"Bank Basket\", \"rule order 1\", \"rule order n\"]}, {\"Event\": \"Rule 1 defines the Bank Basket if a request meets its criteria.\", \"Entity\": [\"Rule 1\", \"Bank Basket\", \"request\", \"criteria\"]}, {\"Event\": \"Rules should be sorted with the most restrictive definition on top.\", \"Entity\": [\"rules\", \"restrictive definition\"]}, {\"Event\": \"A user can select a rule line with a mouse to move it.\", \"Entity\": [\"existing rule\", \"list\", \"mouse\"]}, {\"Event\": \"A user can drag and drop a rule into a desired position.\", \"Entity\": [\"rule\", \"desired position\"]}, {\"Event\": \"Configuration of currency Bank Baskets for Supersonic is not yet available.\", \"Entity\": [\"currency Bank Baskets\", \"Supersonic\"]}, {\"Event\": \"Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.\", \"Entity\": [\"Bank Basket selection\", \"Supersonic interface\", \"SEP Contributors\", \"spot currency pair\"]}, {\"Event\": \"SEP Bank Baskets are used to block SEP providers.\", \"Entity\": [\"SEP Bank Baskets\", \"SEP providers\"]}, {\"Event\": \"The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.\", \"Entity\": [\"Bank Baskets Evaluator Tool\", \"user\", \"rule\", \"particular type of request\"]}, {\"Event\": \"The tool can be accessed from the Bridge Administration Homepage.\", \"Entity\": [\"tool\", \"Bridge Administration Homepage\"]}, {\"Event\": \"The image displays a screenshot of the 360T Bank Baskets Configuration user interface.\", \"Entity\": [\"image\", \"screenshot\", \"360T Bank Baskets Configuration user interface\"]}, {\"Event\": \"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.\", \"Entity\": [\"main area\", \"screen\", \"'Administration Start' page\", \"'Configurations' section\", \"'Actions' section\"]}, {\"Event\": \"Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.\", \"Entity\": [\"'Configurations'\", \"'Regulatory Data' icon\", \"'Bank Baskets' icon\"]}]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 358, "time": 1.0}, "usage_stage_three": {"completion_tokens": 0, "time": 0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "e are icons for 'Change Request', 'Wizards', and 'Evaluator Tools'. The 'Evaluator Tools' icon is highlighted with a red dashed border. The top of the screen includes tabs for 'RFS REQUESTER', 'DEAL TRACKING', and 'BRIDGE ADMINISTRATION', along with options for 'Preferences', 'Administration', and 'Help'. The bottom of the screen displays information about the user's connection and the date and time.\" } Figure 23 Evaluator Tool Quick Link The tool may also be accessed using the Bank Basket Evaluator Tool icon located next to the Configuration Data Tabs. | | | <b>RFS REQUESTER</b><br><b>DEAL TRACKING</b> | <b>BRIDGE ADMINISTRATION</b> | Preferences Administration Help AA - X | | |--|--|--------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------|------------------------------------------------------| | | | Q 360T.ALIAS<br>360T ALIAS Company 1 | Configuration Groups RFS Bank Baskets Orders Bank Baskets SEP Bank Baskets | | | | | | 360T.ALIAS Company 2<br>360T.ALIAS Company 3 | Currency Groups Currency Couple Groups FX Time Period Groups MM Time Period Groups Product Groups | | | | | | 360T.ALIAS Company 4<br>360T.ALIAS Company 5 | Currency Group Default Group | | | | | | 360T ALIAS Company 6 | | Create Group | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | Create Change Request | Discard All Changes | Save | | | | | 360T ALIAS * X 360T ALIAS Company 1 * X | | | | | | 360TAS Treasurer1, 360T ALIAS // INT | | | Fri, 06. Jul 2018, 12:04:09 GMT // Connected [FFM] . | Figure 24 Evaluator Tool icon Both methods will require the user to identify the desired company to evaluate that company's Bank Basket. If using the icon, the system will take the company Bank Basket based on the current active tab. Enter the desired parameters for a particular type of request. Click \"Find Bank Baskets Rule\". Please enter product details | Product Type: | Fx Spot | |------------------|-----------| | Currency Couple: | EUR / USD | Find Bank Baskets RuleFigure 25 Evaluator Tool The system will jump to and highlight the relevant rule. | | <b>DEAL TRACKING</b><br><b>RFS REQUESTER</b> | $+$<br><b>BRIDGE ADMINISTRATION</b> | | | $\\vee$ Preferences $\\vee$ Administration $\\vee$ Help $\\Box$ AA $ \\Box$ X | | |----------------------------------------------------------------|-------------------------------------------------------|-----------------------------------------------------------------|--------------------------------------------|----------------------------|-----------------------------------------------------------------------------|----------| | 合 | Q 豪<br>12<br>!!!!!!!!!!!", "entity_relation_dict": [], "event_entity_relation_dict": [{"Event": "Icons are displayed.", "Entity": ["'Change Request' icon", "'Wizards' icon", "'Evaluator Tools' icon"]}, {"Event": "An icon is highlighted.", "Entity": ["'Evaluator Tools' icon", "red dashed border"]}, {"Event": "The top of the screen contains various interface elements.", "Entity": ["top of the screen", "'RFS REQUESTER' tab", "'DEAL TRACKING' tab", "'BRIDGE ADMINISTRATION' tab", "'Preferences' option", "'Administration' option", "'Help' option"]}, {"Event": "The bottom of the screen shows connection and time details.", "Entity": ["bottom of the screen", "user's connection", "date", "time"]}, {"Event": "The tool can be accessed via an icon.", "Entity": ["tool", "Bank Basket Evaluator Tool icon", "Configuration Data Tabs"]}, {"Event": "User identification of a company is required for evaluation.", "Entity": ["methods", "user", "desired company", "company's Bank Basket"]}, {"Event": "The system selects the company Bank Basket based on the active tab when the icon is used.", "Entity": ["icon", "system", "company Bank Basket", "current active tab"]}, {"Event": "Desired parameters for a request must be entered.", "Entity": ["desired parameters", "particular type of request"]}, {"Event": "The \"Find Bank Baskets Rule\" button should be clicked.", "Entity": ["\"Find Bank Baskets Rule\" button"]}, {"Event": "Product details need to be entered.", "Entity": ["product details"]}, {"Event": "The system will navigate to and highlight a rule.", "Entity": ["system", "relevant rule"]}], "event_relation_dict": [{"Head": "The tool is accessed using the Bank Basket Evaluator Tool icon.", "Relation": "as a result", "Tail": "The user must identify the desired company to evaluate its Bank Basket."}, {"Head": "The user uses the icon to access the tool.", "Relation": "as a result", "Tail": "The system takes the company Bank Basket based on the current active tab."}, {"Head": "The user enters the desired parameters for a request.", "Relation": "before", "Tail": "The user clicks 'Find Bank Baskets Rule'."}, {"Head": "The user clicks 'Find Bank Baskets Rule'.", "Relation": "as a result", "Tail": "The system jumps to and highlights the relevant rule."}], "output_stage_one": "[]", "output_stage_two": "[{\"Event\": \"Icons are displayed.\", \"Entity\": [\"'Change Request' icon\", \"'Wizards' icon\", \"'Evaluator Tools' icon\"]}, {\"Event\": \"An icon is highlighted.\", \"Entity\": [\"'Evaluator Tools' icon\", \"red dashed border\"]}, {\"Event\": \"The top of the screen contains various interface elements.\", \"Entity\": [\"top of the screen\", \"'RFS REQUESTER' tab\", \"'DEAL TRACKING' tab\", \"'BRIDGE ADMINISTRATION' tab\", \"'Preferences' option\", \"'Administration' option\", \"'Help' option\"]}, {\"Event\": \"The bottom of the screen shows connection and time details.\", \"Entity\": [\"bottom of the screen\", \"user's connection\", \"date\", \"time\"]}, {\"Event\": \"The tool can be accessed via an icon.\", \"Entity\": [\"tool\", \"Bank Basket Evaluator Tool icon\", \"Configuration Data Tabs\"]}, {\"Event\": \"User identification of a company is required for evaluation.\", \"Entity\": [\"methods\", \"user\", \"desired company\", \"company's Bank Basket\"]}, {\"Event\": \"The system selects the company Bank Basket based on the active tab when the icon is used.\", \"Entity\": [\"icon\", \"system\", \"company Bank Basket\", \"current active tab\"]}, {\"Event\": \"Desired parameters for a request must be entered.\", \"Entity\": [\"desired parameters\", \"particular type of request\"]}, {\"Event\": \"The \\\"Find Bank Baskets Rule\\\" button should be clicked.\", \"Entity\": [\"\\\"Find Bank Baskets Rule\\\" button\"]}, {\"Event\": \"Product details need to be entered.\", \"Entity\": [\"product details\"]}, {\"Event\": \"The system will navigate to and highlight a rule.\", \"Entity\": [\"system\", \"relevant rule\"]}]", "output_stage_three": "[{\"Head\": \"The tool is accessed using the Bank Basket Evaluator Tool icon.\", \"Relation\": \"as a result\", \"Tail\": \"The user must identify the desired company to evaluate its Bank Basket.\"}, {\"Head\": \"The user uses the icon to access the tool.\", \"Relation\": \"as a result\", \"Tail\": \"The system takes the company Bank Basket based on the current active tab.\"}, {\"Head\": \"The user enters the desired parameters for a request.\", \"Relation\": \"before\", \"Tail\": \"The user clicks 'Find Bank Baskets Rule'.\"}, {\"Head\": \"The user clicks 'Find Bank Baskets Rule'.\", \"Relation\": \"as a result\", \"Tail\": \"The system jumps to and highlights the relevant rule.\"}]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 243, "time": 1.0}, "usage_stage_three": {"completion_tokens": 111, "time": 1.0}}
{"id": "1", "metadata": {"lang": "en"}, "original_text": "!!!!!!!!!!!!!!!!!!!!!!!!!!!!! | <b>Configuration Groups</b><br><b>RFS Bank Baskets</b> | Orders Bank Baskets<br>SEP Bank Baskets | $\\sigma$ | | のの言 | | | △ 盒 360T.ALIAS | RFS Provider Groups<br><b>Blocked RFS Providers</b> | RFS FX Bank Baskets<br>RFS MM Bank Baskets | RFS Commodity Bank Baskets | RFS Cross Currency Netting Bank Baskets | | | $\\mathcal{L}$ | 盒 360T.ALIAS.Company 1 | | | | | | | $\\qquad \\qquad \\boxdot \\qquad$ | 盒 360T.ALIAS.Company 2<br>盒 360T.ALIAS.Company 3 | POS Product Type | Currency Couple | <b>Time Period</b> | <b>Bank Basket</b> | | | $\\frac{d}{d}$ | 盒 360T.ALIAS.Company 4 | : 1 Forwards and Swaps | Scandies | Default Group | Nordic Banks | ショ | | | 盒 360T ALIAS.Company 5 | $2$ Any | ***/AUD | Any | AUD Banks | ジョ | | - | 盒 360T.ALIAS.Company 6 | $\\frac{1}{2}$ 3 Any | AUD/*** | Any | AUD Banks | ジョ | | 厚 | | 4 Any | Any | Any | Default Group | $ V $ ( | | $\\frac{1}{\\sqrt{2}}$ | | | | | | Add Rule | | | | | | | | | | $\\begin{array}{c}\\n\\bullet \\\\ \\bullet \\\\ \\bullet\\n\\end{array}$ | 360TAS.Treasurer1, 360T.ALIAS // INT | Create Change Request<br>360T ALIAS Company 1 X<br>360T.ALIAS X | <b>EECT</b> | | Discard All Changes<br>Fri, 06. Jul 2018, 12:10:31 GMT // Connected [FFM] . | Save | Figure 26 Evaluator Tool: Highlighted Rule ## 5 CONTACT 360T Image /page/21/Picture/2 description: { \"image\\_description\": \"The image shows a logo with the number '360' in a stylized font, followed by a symbol resembling two arrows pointing towards each other within a square, and then the letter 'T'. The logo is primarily green with white accents and is set against a white background.\" } #### Global Support Phone: +49 69 900289-19 Fax: +49 69 900289-29 E-Mail: <EMAIL> #### Germany 360 Treasury Systems AG Grüneburgweg 16-18 60322 Frankfurt am Main Phone: +49 69 900289-0 ### Middle East Asia Pacific ## United Arab Emirates 360 Trading Networks LLC Dubai International Financial Centre Liberty House, Level: 8, App. 810C P.O. Box: 482036 Dubai Phone: +971 4 431 5134 ### EMEA Americas ## USA 360 Trading Networks Inc. 521 Fifth Avenue 38th Floor New York, NY 10175 Phone: ****** 776 2900 ## Singapore 360T Asia Pacific Pte. Ltd. 9 Raffles Place #56-01 Republic Plaza Tower 1 Singapore 048619 Phone: +65 6325 9970 Fax: +65 6597 1756", "entity_relation_dict": [], "event_entity_relation_dict": [], "event_relation_dict": [], "output_stage_one": "[]", "output_stage_two": "[]", "output_stage_three": "[]", "usage_stage_one": {"completion_tokens": 0, "time": 0}, "usage_stage_two": {"completion_tokens": 0, "time": 0}, "usage_stage_three": {"completion_tokens": 1, "time": 1.0}}
