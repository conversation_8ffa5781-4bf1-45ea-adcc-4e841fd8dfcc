name:ID,type,concepts,synsets,:LABEL
The introduction is presented.,event,[],[],Node
Getting started information is provided.,event,[],[],Node
Bank baskets are configured.,event,[],[],Node
The bank baskets evaluator tool is used.,event,[],[],Node
Contact information for 360T is available.,event,[],[],Node
The image shows a logo for 360T.,event,[],[],Node
image,entity,[],[],Node
logo,entity,[],[],Node
360T,entity,[],[],Node
The logo is a green rectangle with rounded corners.,event,[],[],Node
green rectangle,entity,[],[],Node
rounded corners,entity,[],[],Node
The text 360 is in white with a green outline.,event,[],[],Node
text,entity,[],[],Node
360,entity,[],[],Node
white,entity,[],[],Node
green outline,entity,[],[],Node
The T is in white with a green outline.,event,[],[],Node
T,entity,[],[],Node
Two arrows point towards each other in the middle of the 0 in 360.,event,[],[],Node
arrows,entity,[],[],Node
0,entity,[],[],Node
This file contains proprietary and confidential information including trade secrets.,event,[],[],Node
file,entity,[],[],Node
proprietary information,entity,[],[],Node
confidential information,entity,[],[],Node
trade secrets,entity,[],[],Node
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,event,[],[],Node
information,entity,[],[],Node
third party,entity,[],[],Node
written approval,entity,[],[],Node
360 TREASURY SYSTEMS AG,entity,[],[],Node
360 TREASURY SYSTEMS AG holds the copyright for 2019.,event,[],[],Node
copyright,entity,[],[],Node
2019,entity,[],[],Node
user manual,entity,[],[],Node
Bank Basket feature,entity,[],[],Node
360T Bridge Administration tool,entity,[],[],Node
improved rule management capabilities,entity,[],[],Node
configuration groups,entity,[],[],Node
currency,entity,[],[],Node
currency couple,entity,[],[],Node
time period,entity,[],[],Node
product(s),entity,[],[],Node
configuration of separate baskets,entity,[],[],Node
separate baskets,entity,[],[],Node
request type,entity,[],[],Node
This user manual describes the Bank Basket feature.,event,[],[],Node
This user manual,entity,[],[],Node
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,event,[],[],Node
rule management capabilities,entity,[],[],Node
RFS,entity,[],[],Node
Order,entity,[],[],Node
SEP,entity,[],[],Node
temporary blocks,entity,[],[],Node
configured rules,entity,[],[],Node
counterpart relationship(s),entity,[],[],Node
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,event,[],[],Node
360T enhanced Bank Basket feature,entity,[],[],Node
RFS request types,entity,[],[],Node
order request types,entity,[],[],Node
entities,entity,[],[],Node
EMS application,entity,[],[],Node
Bridge applications,entity,[],[],Node
Only users with corresponding user rights are able to administer the company's Bank Baskets.,event,[],[],Node
users,entity,[],[],Node
user rights,entity,[],[],Node
company,entity,[],[],Node
Bank Baskets,entity,[],[],Node
<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.,event,[],[],Node
<EMAIL>,entity,[],[],Node
customer relationship manager,entity,[],[],Node
administrative rights,entity,[],[],Node
The Bank Basket configuration is found within the Bridge Administration tool.,event,[],[],Node
Bank Basket configuration,entity,[],[],Node
Bridge Administration tool,entity,[],[],Node
"Bridge Administration can be accessed via the menu option ""Administration"" in the screen header of the Bridge application.",event,[],[],Node
Bridge Administration,entity,[],[],Node
"menu option ""Administration""",entity,[],[],Node
screen header,entity,[],[],Node
Bridge application,entity,[],[],Node
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,event,[],[],Node
Bridge Administration feature,entity,[],[],Node
homepage,entity,[],[],Node
shortcuts,entity,[],[],Node
configuration tools,entity,[],[],Node
user,entity,[],[],Node
A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.,event,[],[],Node
quick navigation toolbar,entity,[],[],Node
active homepage icon,entity,[],[],Node
Bank Baskets quick link,entity,[],[],Node
navigation panel,entity,[],[],Node
institution tree,entity,[],[],Node
single TEX entity,entity,[],[],Node
TEX main entity,entity,[],[],Node
trade-as entities,entity,[],[],Node
trade-on-behalf entities,entity,[],[],Node
ITEX entities,entity,[],[],Node
single-click,entity,[],[],Node
institution,entity,[],[],Node
new form/sheet,entity,[],[],Node
Bank Basket configuration details,entity,[],[],Node
selected institution,entity,[],[],Node
An institution is selected by a single-click.,event,[],[],Node
A new form or sheet opens with the Bank Basket configuration details.,event,[],[],Node
An item is selected.,event,[],[],Node
The selected item is highlighted as an active task inside the taskbar.,event,[],[],Node
The search icon is activated.,event,[],[],Node
A search field will open.,event,[],[],Node
The user types an alphanumeric value.,event,[],[],Node
The user finds the desired institution.,event,[],[],Node
The user clicks scroll from source.,event,[],[],Node
Jumping to the selected tree item is possible.,event,[],[],Node
The Bank Basket configuration is used.,event,[],[],Node
The 'Show individuals view toggle' icon is deactivated.,event,[],[],Node
"The ""Bank Baskets"" quick link opens a navigation panel.",event,[],[],Node
The navigation panel contains an institution tree.,event,[],[],Node
The tree may include a single TEX entity.,event,[],[],Node
tree,entity,[],[],Node
TEX entity,entity,[],[],Node
The tree may include a TEX main entity.,event,[],[],Node
Trade-as entities are configured under the main entity.,event,[],[],Node
main entity,entity,[],[],Node
Trade-on-behalf entities are configured under the main entity.,event,[],[],Node
ITEX entities are configured under the main entity.,event,[],[],Node
The selection of an institution is done by single-click within the institution tree.,event,[],[],Node
selection,entity,[],[],Node
A single-click within the institution tree opens a new form/sheet.,event,[],[],Node
The new form/sheet contains the Bank Basket configuration details of that entity.,event,[],[],Node
entity,entity,[],[],Node
It is possible to open multiple forms/sheets at a time.,event,[],[],Node
multiple forms/sheets,entity,[],[],Node
The selected item will be highlighted as an active task.,event,[],[],Node
selected item,entity,[],[],Node
active task,entity,[],[],Node
The selected item will be highlighted inside the taskbar.,event,[],[],Node
taskbar,entity,[],[],Node
A set of icons is placed at the top of the navigation panel.,event,[],[],Node
set of icons,entity,[],[],Node
The icons can be activated by a single-click.,event,[],[],Node
icons,entity,[],[],Node
The icons can be deactivated by a single-click.,event,[],[],Node
search field,entity,[],[],Node
The user can type in an alphanumeric value.,event,[],[],Node
alphanumeric value,entity,[],[],Node
The user can find the desired institution.,event,[],[],Node
desired institution,entity,[],[],Node
"The ""Scroll from source"" feature can be used.",event,[],[],Node
Scroll from source feature,entity,[],[],Node
The user desires to find the currently active task/sheet.,event,[],[],Node
currently active task/sheet,entity,[],[],Node
The currently active task/sheet is in the navigation panel.,event,[],[],Node
selected tree item,entity,[],[],Node
Jumping to the active institution in the taskbar is possible.,event,[],[],Node
active institution,entity,[],[],Node
"Jumping is possible when clicking ""scroll from source"".",event,[],[],Node
scroll from source,entity,[],[],Node
"The ""Show individuals view toggle"" icon is deactivated.",event,[],[],Node
Show individuals view toggle icon,entity,[],[],Node
"The ""Show individuals view toggle"" icon is deactivated when using the Bank Basket configuration.",event,[],[],Node
The toggle option allows the user to display only individuals.,event,[],[],Node
toggle option,entity,[],[],Node
individuals,entity,[],[],Node
The individuals are displayed in the navigation panel.,event,[],[],Node
The toggle option is for other configuration tools.,event,[],[],Node
other configuration tools,entity,[],[],Node
"The ""Show institutions view toggle"" icon is deactivated.",event,[],[],Node
Show institutions view toggle icon,entity,[],[],Node
Each entity tab has a Live Audit Log which tracks all unsaved changes.,event,[],[],Node
Individual unsaved changes can be reverted by clicking on the arrow icon.,event,[],[],Node
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",event,[],[],Node
The toggle option allows the user to display only institutions in the navigation panel.,event,[],[],Node
institutions,entity,[],[],Node
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,event,[],[],Node
minimize icon,entity,[],[],Node
panel,entity,[],[],Node
entity tab,entity,[],[],Node
Live Audit Log,entity,[],[],Node
unsaved changes,entity,[],[],Node
arrow icon,entity,[],[],Node
"""Discard all changes"" button",entity,[],[],Node
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,event,[],[],Node
text 'User Guide 360T Bank Baskets Configuration',entity,[],[],Node
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,event,[],[],Node
Configuration Groups tab,entity,[],[],Node
data tabs,entity,[],[],Node
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,event,[],[],Node
Configuration Groups,entity,[],[],Node
parameters,entity,[],[],Node
Configuration Groups allow users to create a group one single time and reuse it across various rules.,event,[],[],Node
group,entity,[],[],Node
rules,entity,[],[],Node
RFS requests can be configured specifically.,event,[],[],Node
RFS requests,entity,[],[],Node
Bank Basket rules for four separate request types are configured on this tab.,event,[],[],Node
Bank Basket rules,entity,[],[],Node
RFS FX Bank Baskets,entity,[],[],Node
RFS MM Bank Baskets,entity,[],[],Node
RFS Commodity Bank Baskets,entity,[],[],Node
RFS Cross Currency Netting Bank Baskets,entity,[],[],Node
this tab,entity,[],[],Node
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,event,[],[],Node
Provider Groups,entity,[],[],Node
temporarily Blocked Providers,entity,[],[],Node
Orders,entity,[],[],Node
Bank Basket rules for Forward or Spot orders are configured on this tab.,event,[],[],Node
Forward orders,entity,[],[],Node
Spot orders,entity,[],[],Node
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,event,[],[],Node
Supersonic (SEP),entity,[],[],Node
Bank Basket rules for SEP streaming spot executions are configured on this tab.,event,[],[],Node
SEP streaming spot executions,entity,[],[],Node
The image shows the logo of DEUTSCHE GROUP.,event,[],[],Node
DEUTSCHE GROUP,entity,[],[],Node
centralized management,entity,[],[],Node
Groups,entity,[],[],Node
each parameter,entity,[],[],Node
creating rules,entity,[],[],Node
groups,entity,[],[],Node
set of rules,entity,[],[],Node
available parameters,entity,[],[],Node
Currency Groups,entity,[],[],Node
Currency Couple Groups,entity,[],[],Node
FX Time Period Groups,entity,[],[],Node
MM Time Period Groups,entity,[],[],Node
create new group,entity,[],[],Node
edit name of existing group,entity,[],[],Node
delete group,entity,[],[],Node
save changes,entity,[],[],Node
Rename Group,entity,[],[],Node
Default Group,entity,[],[],Node
Remove Group,entity,[],[],Node
removed group,entity,[],[],Node
configuring complex bank basket rules,entity,[],[],Node
Users,entity,[],[],Node
individual custom rules,entity,[],[],Node
Individual custom rules,entity,[],[],Node
users with less complex bank basket setups,entity,[],[],Node
Bank Basket Configuration,entity,[],[],Node
existing values,entity,[],[],Node
Default Groups,entity,[],[],Node
values,entity,[],[],Node
new values,entity,[],[],Node
new currency,entity,[],[],Node
Default Currency Group,entity,[],[],Node
single currencies,entity,[],[],Node
single rule for group of currencies,entity,[],[],Node
Currencies,entity,[],[],Node
currency group,entity,[],[],Node
rule creation for interest rate products,entity,[],[],Node
interest rate products,entity,[],[],Node
Loan,entity,[],[],Node
Deposit,entity,[],[],Node
Interest Rate Swap,entity,[],[],Node
FRA,entity,[],[],Node
CapFloor,entity,[],[],Node
Configuration Groups facilitate centralized management of parameters.,event,[],[],Node
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",event,[],[],Node
parameter,entity,[],[],Node
requests,entity,[],[],Node
The groups themselves can be edited without changing a set of rules based on those groups.,event,[],[],Node
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",event,[],[],Node
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",event,[],[],Node
Configuration Group area,entity,[],[],Node
name,entity,[],[],Node
changes,entity,[],[],Node
Create Group adds a new group.,event,[],[],Node
Create Group,entity,[],[],Node
Rename Group changes the name of a group.,event,[],[],Node
Rename Group cannot be used on the Default Group.,event,[],[],Node
Remove Group deletes an individual group.,event,[],[],Node
Remove Group cannot be used on the Default Group.,event,[],[],Node
If the removed group is used in any configured rules this group is replaced by the Default Group.,event,[],[],Node
Users are reminded to save changes to their configurations.,event,[],[],Node
configurations,entity,[],[],Node
Configuration Groups are particularly useful when configuring complex bank basket rules.,event,[],[],Node
bank basket rules,entity,[],[],Node
Tri Party Repo found in the RFS MM Bank Baskets area.,event,[],[],Node
Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.,event,[],[],Node
A default group exists which includes all currencies.,event,[],[],Node
This group cannot be removed or renamed.,event,[],[],Node
The currencies in the group can be altered as described below.,event,[],[],Node
A currency is highlighted with a single-click.,event,[],[],Node
The single arrow is activated.,event,[],[],Node
Clicking the single arrow moves the desired currency.,event,[],[],Node
A user clicks Create Group.,event,[],[],Node
A user types the desired name.,event,[],[],Node
A user clicks Create Group again.,event,[],[],Node
A user clicks Save.,event,[],[],Node
A user clicks on the Currency Group name.,event,[],[],Node
The currencies configured for the group are viewed.,event,[],[],Node
A Currency Couple Group is created.,event,[],[],Node
The Currency Couple Group can be used to simplify rules.,event,[],[],Node
Currencies may be added or removed from the default group.,event,[],[],Node
default group,entity,[],[],Node
A currency,entity,[],[],Node
Highlighting a currency activates the single arrow.,event,[],[],Node
Highlighting a currency,entity,[],[],Node
single arrow,entity,[],[],Node
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,event,[],[],Node
desired currency,entity,[],[],Node
Available,entity,[],[],Node
Selected,entity,[],[],Node
All currencies can be moved in either direction by using the double arrows.,event,[],[],Node
All currencies,entity,[],[],Node
double arrows,entity,[],[],Node
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",event,[],[],Node
new group,entity,[],[],Node
desired name,entity,[],[],Node
Save,entity,[],[],Node
Currencies configured for the group can be viewed by clicking on the Currency Group name.,event,[],[],Node
currencies,entity,[],[],Node
Currency Group name,entity,[],[],Node
The same currency can be added to many different groups.,event,[],[],Node
The system does not restrict the creation of groups with overlapping sets of currencies.,event,[],[],Node
system,entity,[],[],Node
creation of groups,entity,[],[],Node
overlapping sets of currencies,entity,[],[],Node
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",event,[],[],Node
buckets of currency pairs,entity,[],[],Node
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",event,[],[],Node
Currency Couple Group,entity,[],[],Node
FX products,entity,[],[],Node
RFS FX Bank Baskets area,entity,[],[],Node
FX Spot,entity,[],[],Node
Forwards,entity,[],[],Node
Swaps,entity,[],[],Node
NDF,entity,[],[],Node
NDS,entity,[],[],Node
Options,entity,[],[],Node
Block Trades,entity,[],[],Node
Energy Asian Swaps,entity,[],[],Node
Bullet Swaps,entity,[],[],Node
RFS Commodity Bank Baskets area,entity,[],[],Node
Order Spot,entity,[],[],Node
Orders Bank Basket area,entity,[],[],Node
"request type (RFS, Order, SEP)",entity,[],[],Node
ability to apply and remove temporary blocks,entity,[],[],Node
corresponding user rights,entity,[],[],Node
company's Bank Baskets,entity,[],[],Node
relevant administrative rights,entity,[],[],Node
available shortcuts,entity,[],[],Node
different configuration tools,entity,[],[],Node
particular user,entity,[],[],Node
left side of the homepage,entity,[],[],Node
screenshot,entity,[],[],Node
360T Bank Baskets Configuration user guide,entity,[],[],Node
screen,entity,[],[],Node
'Administration Start' page,entity,[],[],Node
'Regulatory Data',entity,[],[],Node
'Bank Baskets',entity,[],[],Node
'Change Request',entity,[],[],Node
'Wizards',entity,[],[],Node
'Evaluator Tools',entity,[],[],Node
top of the screen,entity,[],[],Node
tabs,entity,[],[],Node
'RFS REQUESTER',entity,[],[],Node
'DEAL TRACKING',entity,[],[],Node
'BRIDGE ADMINISTRATION',entity,[],[],Node
preferences options,entity,[],[],Node
help options,entity,[],[],Node
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,event,[],[],Node
The Bank Basket feature has been enhanced.,event,[],[],Node
The enhancement provides improved rule management capabilities.,event,[],[],Node
enhancement,entity,[],[],Node
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",event,[],[],Node
Separate baskets can be configured by request type.,event,[],[],Node
Request types include RFS.,event,[],[],Node
request types,entity,[],[],Node
Request types include Order.,event,[],[],Node
Request types include SEP.,event,[],[],Node
Temporary blocks can be applied.,event,[],[],Node
Temporary blocks can be removed.,event,[],[],Node
Applying temporary blocks does not affect the configured rules.,event,[],[],Node
Figure 2,entity,[],[],Node
Bridge Administration Homepage,entity,[],[],Node
"""Bank Baskets"" quick link",entity,[],[],Node
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",event,[],[],Node
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",event,[],[],Node
trade-as,entity,[],[],Node
trade-on-behalf,entity,[],[],Node
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,event,[],[],Node
The selected item will be highlighted as an active task inside the taskbar.,event,[],[],Node
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,event,[],[],Node
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,event,[],[],Node
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,event,[],[],Node
This feature,entity,[],[],Node
active task/sheet,entity,[],[],Node
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,event,[],[],Node
This icon is deactivated when using the Bank Basket configuration.,event,[],[],Node
This icon,entity,[],[],Node
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,event,[],[],Node
Blocked Providers,entity,[],[],Node
Order Bank Baskets,entity,[],[],Node
SEP Bank Baskets,entity,[],[],Node
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",event,[],[],Node
Bank Basket Configurations,entity,[],[],Node
Orders requests,entity,[],[],Node
SEP requests,entity,[],[],Node
An RFS configuration will have no impact on SEP trading.,event,[],[],Node
RFS configuration,entity,[],[],Node
SEP trading,entity,[],[],Node
Groups can be configured once and reused when creating various rules.,event,[],[],Node
The Rename Group function is to change the name of a group.,event,[],[],Node
The Rename Group function cannot be used on the Default Group.,event,[],[],Node
The Remove Group function is to delete an individual group.,event,[],[],Node
The Remove Group function cannot be used on the Default Group.,event,[],[],Node
A group is removed.,event,[],[],Node
The removed group is replaced by the Default Group if it is used in any configured rules.,event,[],[],Node
It is not required to configure groups based on the above parameters.,event,[],[],Node
Users may still set individual custom rules without utilizing the Configuration Groups.,event,[],[],Node
Users are reminded to save changes to configurations.,event,[],[],Node
complex bank basket rules,entity,[],[],Node
Individual custom rules may be preferable for some users with less complex bank basket setups.,event,[],[],Node
bank basket setups,entity,[],[],Node
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",event,[],[],Node
The Default Group will include all existing values.,event,[],[],Node
All Default Groups can be modified.,event,[],[],Node
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",event,[],[],Node
tool,entity,[],[],Node
versions,entity,[],[],Node
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,event,[],[],Node
360T platform,entity,[],[],Node
The new currency must be selected by the user.,event,[],[],Node
Currency Groups are intended to allow the classification of single currencies into customized groups.,event,[],[],Node
customized groups,entity,[],[],Node
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,event,[],[],Node
rule,entity,[],[],Node
group of currencies,entity,[],[],Node
individual currencies,entity,[],[],Node
Currencies can be added or removed from the group without editing the rules themselves.,event,[],[],Node
The image shows a user interface for managing currency groups in a financial application.,event,[],[],Node
This activates the single arrow.,event,[],[],Node
This,entity,[],[],Node
Click Create Group.,event,[],[],Node
Type the desired name.,event,[],[],Node
Click Create Group again.,event,[],[],Node
Click Save.,event,[],[],Node
Provide a name for a new group.,event,[],[],Node
"To view the currencies configured for the group, click on the Currency Group name.",event,[],[],Node
same currency,entity,[],[],Node
many different groups,entity,[],[],Node
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",event,[],[],Node
Energy Asian and Bullet Swaps,entity,[],[],Node
The Default Group contains all currency pairs.,event,[],[],Node
currency pairs,entity,[],[],Node
base currency,entity,[],[],Node
quote currency,entity,[],[],Node
Currency Couple Groups can be created.,event,[],[],Node
Configuration Group icons,entity,[],[],Node
Currency Couple Groups can be renamed.,event,[],[],Node
Currency Couple Groups can be removed.,event,[],[],Node
A user can add currency pairs within a group.,event,[],[],Node
The Add Currency Couple button is clicked.,event,[],[],Node
Add Currency Couple button,entity,[],[],Node
The ISO code is chosen in the drop down list.,event,[],[],Node
ISO code,entity,[],[],Node
drop down list,entity,[],[],Node
The desired currency is typed.,event,[],[],Node
The selection is confirmed by clicking the green check mark.,event,[],[],Node
green check mark,entity,[],[],Node
Save is clicked.,event,[],[],Node
Products with varying maturities or tenors may be configured into maturity ranges.,event,[],[],Node
Products,entity,[],[],Node
maturities,entity,[],[],Node
tenors,entity,[],[],Node
maturity ranges,entity,[],[],Node
An FX Time Period Group can simplify rules for FX products.,event,[],[],Node
FX Time Period Group,entity,[],[],Node
The Add FX Time Period button is clicked.,event,[],[],Node
The desired time period is chosen.,event,[],[],Node
The Save button is clicked.,event,[],[],Node
The Add MM Time Period button is clicked.,event,[],[],Node
The desired time period is selected.,event,[],[],Node
An MM Time Period Group is created.,event,[],[],Node
Rules for interest rate products can be simplified.,event,[],[],Node
The same tenors are used in various groups.,event,[],[],Node
They are used for different sets of rules.,event,[],[],Node
Tenors,entity,[],[],Node
range of maturities,entity,[],[],Node
start and end values,entity,[],[],Node
various groups,entity,[],[],Node
Product Groups,entity,[],[],Node
product types,entity,[],[],Node
single rule,entity,[],[],Node
product group,entity,[],[],Node
Product group,entity,[],[],Node
rule creation,entity,[],[],Node
Rule creation,entity,[],[],Node
relevant product types,entity,[],[],Node
Default group,entity,[],[],Node
all products,entity,[],[],Node
removed,entity,[],[],Node
renamed,entity,[],[],Node
Product types,entity,[],[],Node
Relevant products,entity,[],[],Node
default Product Group,entity,[],[],Node
Bank Basket areas,entity,[],[],Node
providers,entity,[],[],Node
User interface,entity,[],[],Node
RFS Requester option,entity,[],[],Node
Deal Tracking option,entity,[],[],Node
Bridge Administration option,entity,[],[],Node
currency groups,entity,[],[],Node
FX time period groups,entity,[],[],Node
MM time period groups,entity,[],[],Node
product groups,entity,[],[],Node
"""Select Member for 'FX Spot and Forward'"" section",entity,[],[],Node
available product types,entity,[],[],Node
selected product types,entity,[],[],Node
Product Groups classify product types into customized groups,event,[],[],Node
One single rule can be set for each group of products,event,[],[],Node
A product group is created,event,[],[],Node
A product group can be used to simplify rule creation,event,[],[],Node
Click Create Group,event,[],[],Node
Type the desired name,event,[],[],Node
Click Create Group again,event,[],[],Node
Click Save,event,[],[],Node
A default group exists which includes all products,event,[],[],Node
This default group cannot be removed or renamed,event,[],[],Node
A rule utilizes the default Product Group,event,[],[],Node
"Only the relevant products for RFS, Orders or SEP will apply",event,[],[],Node
Periods can be added for specific durations.,event,[],[],Node
periods,entity,[],[],Node
OVERNIGHT,entity,[],[],Node
1 WEEK,entity,[],[],Node
1 MONTH,entity,[],[],Node
6 MONTHS,entity,[],[],Node
Tenors are defined as a range of maturities.,event,[],[],Node
start values,entity,[],[],Node
end values,entity,[],[],Node
The same tenors can be used in various groups for different sets of rules.,event,[],[],Node
sets of rules,entity,[],[],Node
Product Groups are intended to classify product types into customized groups.,event,[],[],Node
This allows setting a single rule for each group of products.,event,[],[],Node
group of products,entity,[],[],Node
The image displays a user interface for configuring 360T Bank Baskets.,event,[],[],Node
user interface,entity,[],[],Node
360T Bank Baskets,entity,[],[],Node
"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.",event,[],[],Node
RFS Requester,entity,[],[],Node
Deal Tracking,entity,[],[],Node
The interface enables users to manage various groups.,event,[],[],Node
interface,entity,[],[],Node
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible.",event,[],[],Node
section,entity,[],[],Node
FX Spot and Forward,entity,[],[],Node
Products can be added or removed from the group.,event,[],[],Node
A product group can simplify rule creation for relevant product types.,event,[],[],Node
SEP Bank Basket areas,entity,[],[],Node
"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",event,[],[],Node
A default group exists that includes all products.,event,[],[],Node
products,entity,[],[],Node
The product types in the group can be altered.,event,[],[],Node
"The Default Group contains all product types across RFS, Orders and SEP.",event,[],[],Node
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",event,[],[],Node
Each Bank Basket area allows creating Provider Groups and blocking providers.,event,[],[],Node
"The image displays a green button with ""360T"" text.",event,[],[],Node
green button,entity,[],[],Node
The button features a white outline and a white background.,event,[],[],Node
button,entity,[],[],Node
white outline,entity,[],[],Node
white background,entity,[],[],Node
The text is presented in a stylized font.,event,[],[],Node
stylized font,entity,[],[],Node
Providers are temporarily blocked from particular request types.,event,[],[],Node
"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.",event,[],[],Node
A Provider should be removed completely.,event,[],[],Node
The relationship should be rejected using the Counterpart Relationship Management tool.,event,[],[],Node
The relationship is rejected using the Counterpart Relationship Management tool.,event,[],[],Node
Please refer to the relevant user guide.,event,[],[],Node
Provider Groups can be edited.,event,[],[],Node
Providers can be temporarily blocked from particular request types.,event,[],[],Node
Providers,entity,[],[],Node
Provider Group,entity,[],[],Node
"A Blocked Provider will remain in a Provider Group and appear with a ""blocked"" symbol.",event,[],[],Node
Blocked Provider,entity,[],[],Node
"""blocked"" symbol",entity,[],[],Node
A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.,event,[],[],Node
Provider,entity,[],[],Node
relationship,entity,[],[],Node
Counterpart Relationship Management tool,entity,[],[],Node
Users should refer to the relevant user guide.,event,[],[],Node
user guide,entity,[],[],Node
The Default Group will include all active-relationship providers.,event,[],[],Node
All Bank Baskets will contain one single rule.,event,[],[],Node
New Counterparties will be added to the Default Group.,event,[],[],Node
All configuration groups are in place.,event,[],[],Node
Individual rules may be defined using the relevant groups for each Bank Basket area.,event,[],[],Node
Click Add Rule.,event,[],[],Node
Select an option from each of the configuration groups using the drop down menu.,event,[],[],Node
The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.,event,[],[],Node
active-relationship providers,entity,[],[],Node
Counterpart Relationship,entity,[],[],Node
New Counterparties,entity,[],[],Node
A user clicks Create Group to add a new group.,event,[],[],Node
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",event,[],[],Node
banks,entity,[],[],Node
Available Providers,entity,[],[],Node
Selected Providers,entity,[],[],Node
arrow buttons,entity,[],[],Node
The user selects 'Custom' in a rule parameter.,event,[],[],Node
A criteria can be defined in that moment.,event,[],[],Node
A predefined Configuration group is not needed.,event,[],[],Node
No Currency Couple Group was defined for AUD and a specific bank basket should be used for all AUD requests.,event,[],[],Node
The rules shown in the figure below can be defined.,event,[],[],Node
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",event,[],[],Node
Configuration Group,entity,[],[],Node
previously saved groups,entity,[],[],Node
Any,entity,[],[],Node
Custom,entity,[],[],Node
"The selection of ""Custom"" in each rule parameter allows to define a criteria in that moment.",event,[],[],Node
rule parameter,entity,[],[],Node
criteria,entity,[],[],Node
predefined Configuration group,entity,[],[],Node
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,event,[],[],Node
figure below,entity,[],[],Node
AUD,entity,[],[],Node
bank basket,entity,[],[],Node
AUD requests,entity,[],[],Node
"The image contains the text '360T' in a green, stylized font on a white background.",event,[],[],Node
text '360T',entity,[],[],Node
"green, stylized font",entity,[],[],Node
The text appears to be a logo or brand name.,event,[],[],Node
brand name,entity,[],[],Node
"A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.",event,[],[],Node
connection,entity,[],[],Node
"Fri, 06. Jul 2018, 11:28:21 GMT",entity,[],[],Node
FFM,entity,[],[],Node
RFS requests will use the AUD-Group bank basket based on currency rules.,event,[],[],Node
base currency AUD,entity,[],[],Node
Rule 2,entity,[],[],Node
terms currency AUD,entity,[],[],Node
Rule 3,entity,[],[],Node
AUD-Group bank basket,entity,[],[],Node
The tenor can determine the banks for sending a request.,event,[],[],Node
tenor,entity,[],[],Node
request,entity,[],[],Node
The POS column indicates rule precedence.,event,[],[],Node
POS column,entity,[],[],Node
The Bank Basket is chosen for each request based on rule order.,event,[],[],Node
individual request,entity,[],[],Node
Bank Basket,entity,[],[],Node
rule order 1,entity,[],[],Node
rule order n,entity,[],[],Node
Rule 1 defines the Bank Basket if a request meets its criteria.,event,[],[],Node
Rule 1,entity,[],[],Node
Rules should be sorted with the most restrictive definition on top.,event,[],[],Node
restrictive definition,entity,[],[],Node
A user can select a rule line with a mouse to move it.,event,[],[],Node
existing rule,entity,[],[],Node
list,entity,[],[],Node
mouse,entity,[],[],Node
A user can drag and drop a rule into a desired position.,event,[],[],Node
desired position,entity,[],[],Node
Configuration of currency Bank Baskets for Supersonic is not yet available.,event,[],[],Node
currency Bank Baskets,entity,[],[],Node
Supersonic,entity,[],[],Node
Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.,event,[],[],Node
Bank Basket selection,entity,[],[],Node
Supersonic interface,entity,[],[],Node
SEP Contributors,entity,[],[],Node
spot currency pair,entity,[],[],Node
SEP Bank Baskets are used to block SEP providers.,event,[],[],Node
SEP providers,entity,[],[],Node
The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.,event,[],[],Node
Bank Baskets Evaluator Tool,entity,[],[],Node
particular type of request,entity,[],[],Node
The tool can be accessed from the Bridge Administration Homepage.,event,[],[],Node
The image displays a screenshot of the 360T Bank Baskets Configuration user interface.,event,[],[],Node
360T Bank Baskets Configuration user interface,entity,[],[],Node
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",event,[],[],Node
main area,entity,[],[],Node
'Configurations' section,entity,[],[],Node
'Actions' section,entity,[],[],Node
Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.,event,[],[],Node
'Configurations',entity,[],[],Node
'Regulatory Data' icon,entity,[],[],Node
'Bank Baskets' icon,entity,[],[],Node
The tool is accessed using the Bank Basket Evaluator Tool icon.,event,[],[],Node
The user must identify the desired company to evaluate its Bank Basket.,event,[],[],Node
The user uses the icon to access the tool.,event,[],[],Node
The system takes the company Bank Basket based on the current active tab.,event,[],[],Node
The user enters the desired parameters for a request.,event,[],[],Node
The user clicks 'Find Bank Baskets Rule'.,event,[],[],Node
The system jumps to and highlights the relevant rule.,event,[],[],Node
Icons are displayed.,event,[],[],Node
'Change Request' icon,entity,[],[],Node
'Wizards' icon,entity,[],[],Node
'Evaluator Tools' icon,entity,[],[],Node
An icon is highlighted.,event,[],[],Node
red dashed border,entity,[],[],Node
The top of the screen contains various interface elements.,event,[],[],Node
'RFS REQUESTER' tab,entity,[],[],Node
'DEAL TRACKING' tab,entity,[],[],Node
'BRIDGE ADMINISTRATION' tab,entity,[],[],Node
'Preferences' option,entity,[],[],Node
'Administration' option,entity,[],[],Node
'Help' option,entity,[],[],Node
The bottom of the screen shows connection and time details.,event,[],[],Node
bottom of the screen,entity,[],[],Node
user's connection,entity,[],[],Node
date,entity,[],[],Node
time,entity,[],[],Node
The tool can be accessed via an icon.,event,[],[],Node
Bank Basket Evaluator Tool icon,entity,[],[],Node
Configuration Data Tabs,entity,[],[],Node
User identification of a company is required for evaluation.,event,[],[],Node
methods,entity,[],[],Node
desired company,entity,[],[],Node
company's Bank Basket,entity,[],[],Node
The system selects the company Bank Basket based on the active tab when the icon is used.,event,[],[],Node
icon,entity,[],[],Node
company Bank Basket,entity,[],[],Node
current active tab,entity,[],[],Node
Desired parameters for a request must be entered.,event,[],[],Node
desired parameters,entity,[],[],Node
"The ""Find Bank Baskets Rule"" button should be clicked.",event,[],[],Node
"""Find Bank Baskets Rule"" button",entity,[],[],Node
Product details need to be entered.,event,[],[],Node
product details,entity,[],[],Node
The system will navigate to and highlight a rule.,event,[],[],Node
relevant rule,entity,[],[],Node
