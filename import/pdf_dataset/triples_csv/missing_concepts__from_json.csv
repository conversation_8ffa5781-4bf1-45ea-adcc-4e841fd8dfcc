Name,Type
currencies,Entity
Bridge Administration,Entity
currency couple,Entity
trade-as entities,Entity
main entity,Entity
ability to apply and remove temporary blocks,Entity
system,Entity
"request type (RFS, Order, SEP)",Entity
active-relationship providers,Entity
360 TREASURY SYSTEMS AG,Entity
Supersonic,Entity
trade-on-behalf,Entity
Bank Basket Configurations,Entity
'RFS REQUESTER',Entity
Bank Basket Configuration,Entity
Show institutions view toggle icon,Entity
entity tab,Entity
user manual,Entity
'Actions' section,Entity
Rename Group,Entity
white outline,Entity
white background,Entity
Interest Rate Swap,Entity
bank basket setups,Entity
interface,Entity
button,Entity
Tenors,Entity
Bullet Swaps,Entity
text,Entity
previously saved groups,Entity
RFS MM Bank Baskets,Entity
all products,Entity
desired name,Entity
new group,Entity
user guide,Entity
restrictive definition,Entity
'Bank Baskets' icon,Entity
'Administration Start' page,Entity
360T Bank Baskets Configuration user interface,Entity
terms currency AUD,Entity
Groups,Entity
selection,Entity
arrow buttons,Entity
desired position,Entity
"menu option ""Administration""",Entity
institutions,Entity
removed,Entity
TEX main entity,Entity
Orders,Entity
minimize icon,Entity
360T,Entity
Bank Basket areas,Entity
Bank Basket configuration,Entity
This user manual,Entity
RFS FX Bank Baskets area,Entity
Bank Basket selection,Entity
product(s),Entity
institution,Entity
groups,Entity
Users,Entity
user interface,Entity
particular type of request,Entity
brand name,Entity
separate baskets,Entity
product group,Entity
Add Currency Couple button,Entity
A currency,Entity
proprietary information,Entity
tree,Entity
toggle option,Entity
new form/sheet,Entity
stylized font,Entity
selected tree item,Entity
save changes,Entity
user,Entity
users with less complex bank basket setups,Entity
configurations,Entity
Configuration Groups tab,Entity
each parameter,Entity
spot currency pair,Entity
unsaved changes,Entity
POS column,Entity
T,Entity
product types,Entity
FX Spot,Entity
Orders requests,Entity
Default Groups,Entity
Forwards,Entity
start and end values,Entity
configuration of separate baskets,Entity
Providers,Entity
This feature,Entity
RFS requests,Entity
corresponding user rights,Entity
single rule for group of currencies,Entity
AUD-Group bank basket,Entity
RFS Commodity Bank Baskets,Entity
Bank Basket configuration details,Entity
product groups,Entity
Bank Baskets Evaluator Tool,Entity
changes,Entity
icons,Entity
left side of the homepage,Entity
image,Entity
Bridge applications,Entity
entities,Entity
customized groups,Entity
red dashed border,Entity
bottom of the screen,Entity
selected item,Entity
third party,Entity
6 MONTHS,Entity
'Regulatory Data' icon,Entity
Orders Bank Basket area,Entity
logo,Entity
DEUTSCHE GROUP,Entity
providers,Entity
figure below,Entity
"""Bank Baskets"" quick link",Entity
section,Entity
information,Entity
screenshot,Entity
predefined Configuration group,Entity
ISO code,Entity
homepage,Entity
copyright,Entity
relevant rule,Entity
FRA,Entity
single rule,Entity
Products,Entity
CapFloor,Entity
double arrows,Entity
Provider Groups,Entity
Bridge Administration feature,Entity
help options,Entity
single currencies,Entity
SEP streaming spot executions,Entity
active institution,Entity
Blocked Provider,Entity
icon,Entity
Highlighting a currency,Entity
360T Bank Baskets Configuration user guide,Entity
Order,Entity
RFS,Entity
administrative rights,Entity
Rule 1,Entity
Deal Tracking option,Entity
arrows,Entity
parameters,Entity
creation of groups,Entity
navigation panel,Entity
Individual custom rules,Entity
User interface,Entity
Currency Groups,Entity
Available Providers,Entity
desired institution,Entity
request type,Entity
base currency AUD,Entity
NDS,Entity
company,Entity
individual request,Entity
Configuration Group,Entity
other configuration tools,Entity
Any,Entity
available parameters,Entity
group,Entity
RFS FX Bank Baskets,Entity
set of rules,Entity
order request types,Entity
tenors,Entity
requests,Entity
group of currencies,Entity
FX products,Entity
Bank Basket Evaluator Tool icon,Entity
base currency,Entity
default Product Group,Entity
quote currency,Entity
Default Currency Group,Entity
'Help' option,Entity
Provider,Entity
rounded corners,Entity
Bank Basket feature,Entity
data tabs,Entity
Currency Couple Groups,Entity
Scroll from source feature,Entity
group of products,Entity
file,Entity
Order Spot,Entity
available product types,Entity
maturity ranges,Entity
Counterpart Relationship Management tool,Entity
Currencies,Entity
Deal Tracking,Entity
Configuration Group icons,Entity
confidential information,Entity
quick navigation toolbar,Entity
Available,Entity
'Administration' option,Entity
available shortcuts,Entity
SEP Bank Baskets,Entity
Currency Group name,Entity
users,Entity
enhancement,Entity
Bridge Administration Homepage,Entity
user's connection,Entity
name,Entity
All currencies,Entity
range of maturities,Entity
time period,Entity
SEP Bank Basket areas,Entity
values,Entity
buckets of currency pairs,Entity
'BRIDGE ADMINISTRATION',Entity
active task,Entity
FX Time Period Groups,Entity
mouse,Entity
"""Find Bank Baskets Rule"" button",Entity
different configuration tools,Entity
Energy Asian and Bullet Swaps,Entity
single-click,Entity
Configuration Groups,Entity
RFS Cross Currency Netting Bank Baskets,Entity
Loan,Entity
SEP trading,Entity
company Bank Basket,Entity
complex bank basket rules,Entity
edit name of existing group,Entity
rule parameter,Entity
'Evaluator Tools',Entity
default group,Entity
Create Group,Entity
SEP providers,Entity
Forward orders,Entity
criteria,Entity
"""Discard all changes"" button",Entity
'Configurations' section,Entity
Counterpart Relationship,Entity
centralized management,Entity
'BRIDGE ADMINISTRATION' tab,Entity
This icon,Entity
360T enhanced Bank Basket feature,Entity
Bank Baskets,Entity
trade-as,Entity
currency,Entity
banks,Entity
360T Bank Baskets,Entity
rule creation for interest rate products,Entity
'DEAL TRACKING' tab,Entity
existing values,Entity
single TEX entity,Entity
'Wizards' icon,Entity
Supersonic interface,Entity
customer relationship manager,Entity
Save,Entity
tool,Entity
rule order n,Entity
AUD requests,Entity
green rectangle,Entity
360T Bridge Administration tool,Entity
overlapping sets of currencies,Entity
relevant administrative rights,Entity
written approval,Entity
ITEX entities,Entity
Deposit,Entity
Figure 2,Entity
Provider Group,Entity
selected product types,Entity
2019,Entity
Bank Baskets quick link,Entity
tabs,Entity
same currency,Entity
trade secrets,Entity
connection,Entity
Blocked Providers,Entity
new values,Entity
Bridge Administration tool,Entity
0,Entity
user rights,Entity
SEP Contributors,Entity
configuration tools,Entity
bank basket,Entity
request,Entity
This,Entity
'Evaluator Tools' icon,Entity
individuals,Entity
360T platform,Entity
particular user,Entity
configuration groups,Entity
currency Bank Baskets,Entity
New Counterparties,Entity
search field,Entity
Show individuals view toggle icon,Entity
Configuration Group area,Entity
Bridge Administration option,Entity
rule order 1,Entity
top of the screen,Entity
bank basket rules,Entity
trade-on-behalf entities,Entity
company's Bank Basket,Entity
panel,Entity
sets of rules,Entity
counterpart relationship(s),Entity
rules,Entity
'Change Request' icon,Entity
Selected Providers,Entity
rule management capabilities,Entity
<EMAIL>,Entity
green button,Entity
Options,Entity
Spot orders,Entity
existing rule,Entity
single arrow,Entity
active task/sheet,Entity
white,Entity
start values,Entity
drop down list,Entity
this tab,Entity
list,Entity
Rule 2,Entity
scroll from source,Entity
product details,Entity
SEP requests,Entity
Default group,Entity
tenor,Entity
new currency,Entity
parameter,Entity
Product types,Entity
RFS Requester,Entity
Relevant products,Entity
RFS configuration,Entity
SEP,Entity
removed group,Entity
preferences options,Entity
1 MONTH,Entity
multiple forms/sheets,Entity
Selected,Entity
Bank Basket,Entity
individual currencies,Entity
Rule creation,Entity
current active tab,Entity
set of icons,Entity
Configuration Data Tabs,Entity
screen,Entity
Energy Asian Swaps,Entity
text '360T',Entity
arrow icon,Entity
periods,Entity
shortcuts,Entity
desired company,Entity
Live Audit Log,Entity
many different groups,Entity
desired parameters,Entity
delete group,Entity
currently active task/sheet,Entity
creating rules,Entity
maturities,Entity
"""Select Member for 'FX Spot and Forward'"" section",Entity
screen header,Entity
Swaps,Entity
NDF,Entity
temporarily Blocked Providers,Entity
"Fri, 06. Jul 2018, 11:28:21 GMT",Entity
Product group,Entity
'Bank Baskets',Entity
Default Group,Entity
TEX entity,Entity
renamed,Entity
RFS Commodity Bank Baskets area,Entity
Currency Couple Group,Entity
institution tree,Entity
text 'User Guide 360T Bank Baskets Configuration',Entity
entity,Entity
improved rule management capabilities,Entity
360,Entity
Custom,Entity
relationship,Entity
Order Bank Baskets,Entity
individual custom rules,Entity
create new group,Entity
Bridge application,Entity
main area,Entity
MM Time Period Groups,Entity
'Wizards',Entity
company's Bank Baskets,Entity
OVERNIGHT,Entity
"""blocked"" symbol",Entity
Supersonic (SEP),Entity
interest rate products,Entity
versions,Entity
'Preferences' option,Entity
request types,Entity
products,Entity
'DEAL TRACKING',Entity
currency group,Entity
Bank Basket rules,Entity
taskbar,Entity
active homepage icon,Entity
end values,Entity
FX Spot and Forward,Entity
selected institution,Entity
configuring complex bank basket rules,Entity
rule,Entity
"green, stylized font",Entity
'RFS REQUESTER' tab,Entity
FFM,Entity
FX Time Period Group,Entity
EMS application,Entity
configured rules,Entity
Product Groups,Entity
date,Entity
Remove Group,Entity
1 WEEK,Entity
'Configurations',Entity
Block Trades,Entity
currency pairs,Entity
currency groups,Entity
methods,Entity
FX time period groups,Entity
'Regulatory Data',Entity
time,Entity
RFS request types,Entity
rule creation,Entity
desired currency,Entity
various groups,Entity
AUD,Entity
green outline,Entity
'Change Request',Entity
Rule 3,Entity
relevant product types,Entity
MM time period groups,Entity
temporary blocks,Entity
RFS Requester option,Entity
alphanumeric value,Entity
green check mark,Entity
The same currency can be added to many different groups.,Event
The removed group is replaced by the Default Group if it is used in any configured rules.,Event
The button features a white outline and a white background.,Event
The image displays a screenshot of the 360T Bank Baskets Configuration user interface.,Event
The Rename Group function is to change the name of a group.,Event
Please refer to the relevant user guide.,Event
Provide a name for a new group.,Event
A user clicks Create Group to add a new group.,Event
A rule utilizes the default Product Group,Event
An FX Time Period Group can simplify rules for FX products.,Event
Tenors are defined as a range of maturities.,Event
The Save button is clicked.,Event
"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.",Event
The user desires to find the currently active task/sheet.,Event
The image shows a user interface for managing currency groups in a financial application.,Event
Trade-on-behalf entities are configured under the main entity.,Event
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Event
Users are reminded to save changes to their configurations.,Event
A Provider should be removed completely.,Event
The Default Group will include all active-relationship providers.,Event
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",Event
The image shows a logo for 360T.,Event
"The ""Show individuals view toggle"" icon is deactivated.",Event
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,Event
The new form/sheet contains the Bank Basket configuration details of that entity.,Event
New Counterparties will be added to the Default Group.,Event
"The image contains the text '360T' in a green, stylized font on a white background.",Event
Create Group adds a new group.,Event
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Event
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,Event
The groups themselves can be edited without changing a set of rules based on those groups.,Event
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",Event
"The ""Bank Baskets"" quick link opens a navigation panel.",Event
Periods can be added for specific durations.,Event
"Jumping is possible when clicking ""scroll from source"".",Event
Configuration Groups facilitate centralized management of parameters.,Event
Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.,Event
Save is clicked.,Event
The image shows the logo of DEUTSCHE GROUP.,Event
The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.,Event
A group is removed.,Event
Click Save.,Event
Bank Basket rules for Forward or Spot orders are configured on this tab.,Event
Configuration of currency Bank Baskets for Supersonic is not yet available.,Event
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,Event
Currency Couple Groups can be removed.,Event
A currency is highlighted with a single-click.,Event
The tool is accessed using the Bank Basket Evaluator Tool icon.,Event
An icon is highlighted.,Event
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Event
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,Event
If the removed group is used in any configured rules this group is replaced by the Default Group.,Event
The user uses the icon to access the tool.,Event
Configuration Groups are particularly useful when configuring complex bank basket rules.,Event
Contact information for 360T is available.,Event
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",Event
The user enters the desired parameters for a request.,Event
Individual rules may be defined using the relevant groups for each Bank Basket area.,Event
A user clicks on the Currency Group name.,Event
All configuration groups are in place.,Event
Users may still set individual custom rules without utilizing the Configuration Groups.,Event
Type the desired name.,Event
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",Event
The toggle option allows the user to display only individuals.,Event
The selection is confirmed by clicking the green check mark.,Event
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",Event
An RFS configuration will have no impact on SEP trading.,Event
The individuals are displayed in the navigation panel.,Event
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Event
A new form or sheet opens with the Bank Basket configuration details.,Event
"The image displays a green button with ""360T"" text.",Event
Currency Couple Groups can be renamed.,Event
The Rename Group function cannot be used on the Default Group.,Event
Individual unsaved changes can be reverted by clicking on the arrow icon.,Event
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Event
Rules for interest rate products can be simplified.,Event
RFS requests will use the AUD-Group bank basket based on currency rules.,Event
The Bank Basket is chosen for each request based on rule order.,Event
The user must identify the desired company to evaluate its Bank Basket.,Event
Currency Groups are intended to allow the classification of single currencies into customized groups.,Event
Click Create Group again,Event
The system will navigate to and highlight a rule.,Event
Products with varying maturities or tenors may be configured into maturity ranges.,Event
The T is in white with a green outline.,Event
An institution is selected by a single-click.,Event
Jumping to the selected tree item is possible.,Event
Currency Couple Groups can be created.,Event
A default group exists which includes all products,Event
Users are reminded to save changes to configurations.,Event
The user selects 'Custom' in a rule parameter.,Event
The text appears to be a logo or brand name.,Event
The tenor can determine the banks for sending a request.,Event
All currencies can be moved in either direction by using the double arrows.,Event
Each Bank Basket area allows creating Provider Groups and blocking providers.,Event
The currencies in the group can be altered as described below.,Event
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",Event
The system takes the company Bank Basket based on the current active tab.,Event
The search icon is activated.,Event
Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.,Event
The desired currency is typed.,Event
Applying temporary blocks does not affect the configured rules.,Event
A user can add currency pairs within a group.,Event
This allows setting a single rule for each group of products.,Event
The interface enables users to manage various groups.,Event
A user clicks Save.,Event
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,Event
The same tenors can be used in various groups for different sets of rules.,Event
The Bank Basket feature has been enhanced.,Event
A product group can simplify rule creation for relevant product types.,Event
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",Event
The Default Group will include all existing values.,Event
A user types the desired name.,Event
A set of icons is placed at the top of the navigation panel.,Event
The selected item will be highlighted as an active task.,Event
A default group exists which includes all currencies.,Event
Bank Basket rules for SEP streaming spot executions are configured on this tab.,Event
Tri Party Repo found in the RFS MM Bank Baskets area.,Event
Separate baskets can be configured by request type.,Event
The user types an alphanumeric value.,Event
Icons are displayed.,Event
Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.,Event
Configuration Groups allow users to create a group one single time and reuse it across various rules.,Event
The toggle option allows the user to display only institutions in the navigation panel.,Event
The Currency Couple Group can be used to simplify rules.,Event
Desired parameters for a request must be entered.,Event
"The selection of ""Custom"" in each rule parameter allows to define a criteria in that moment.",Event
"The ""Scroll from source"" feature can be used.",Event
Bank Basket rules for four separate request types are configured on this tab.,Event
Currencies may be added or removed from the default group.,Event
This default group cannot be removed or renamed,Event
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,Event
The text 360 is in white with a green outline.,Event
The Bank Basket configuration is used.,Event
Trade-as entities are configured under the main entity.,Event
Highlighting a currency activates the single arrow.,Event
Select an option from each of the configuration groups using the drop down menu.,Event
Request types include RFS.,Event
The same tenors are used in various groups.,Event
A Currency Couple Group is created.,Event
The system does not restrict the creation of groups with overlapping sets of currencies.,Event
A criteria can be defined in that moment.,Event
SEP Bank Baskets are used to block SEP providers.,Event
Providers are temporarily blocked from particular request types.,Event
The user clicks scroll from source.,Event
Request types include SEP.,Event
The selected item is highlighted as an active task inside the taskbar.,Event
Providers can be temporarily blocked from particular request types.,Event
The icons can be activated by a single-click.,Event
The selected item will be highlighted inside the taskbar.,Event
Rename Group cannot be used on the Default Group.,Event
The navigation panel contains an institution tree.,Event
A product group is created,Event
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,Event
Temporary blocks can be removed.,Event
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,Event
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Event
The Add MM Time Period button is clicked.,Event
User identification of a company is required for evaluation.,Event
The Add Currency Couple button is clicked.,Event
A default group exists that includes all products.,Event
A user clicks Create Group again.,Event
The bank baskets evaluator tool is used.,Event
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,Event
All Bank Baskets will contain one single rule.,Event
A user can drag and drop a rule into a desired position.,Event
An item is selected.,Event
"The Default Group contains all product types across RFS, Orders and SEP.",Event
"The ""Show institutions view toggle"" icon is deactivated.",Event
RFS requests can be configured specifically.,Event
The selected item will be highlighted as an active task inside the taskbar.,Event
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,Event
Rules should be sorted with the most restrictive definition on top.,Event
"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Event
The tool can be accessed via an icon.,Event
Clicking the single arrow moves the desired currency.,Event
This file contains proprietary and confidential information including trade secrets.,Event
Two arrows point towards each other in the middle of the 0 in 360.,Event
The tree may include a TEX main entity.,Event
Rename Group changes the name of a group.,Event
"The ""Show individuals view toggle"" icon is deactivated when using the Bank Basket configuration.",Event
This group cannot be removed or renamed.,Event
"A Blocked Provider will remain in a Provider Group and appear with a ""blocked"" symbol.",Event
The relationship is rejected using the Counterpart Relationship Management tool.,Event
Product details need to be entered.,Event
The Bank Basket configuration is found within the Bridge Administration tool.,Event
Type the desired name,Event
"The ""Find Bank Baskets Rule"" button should be clicked.",Event
A single-click within the institution tree opens a new form/sheet.,Event
The icons can be deactivated by a single-click.,Event
Provider Groups can be edited.,Event
The user can type in an alphanumeric value.,Event
Request types include Order.,Event
The user can find the desired institution.,Event
A user can select a rule line with a mouse to move it.,Event
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,Event
Temporary blocks can be applied.,Event
Click Create Group again.,Event
Click Create Group.,Event
The system jumps to and highlights the relevant rule.,Event
This user manual describes the Bank Basket feature.,Event
The system selects the company Bank Basket based on the active tab when the icon is used.,Event
Jumping to the active institution in the taskbar is possible.,Event
The tool can be accessed from the Bridge Administration Homepage.,Event
The Remove Group function is to delete an individual group.,Event
It is possible to open multiple forms/sheets at a time.,Event
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,Event
Currencies configured for the group can be viewed by clicking on the Currency Group name.,Event
Individual custom rules may be preferable for some users with less complex bank basket setups.,Event
"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.",Event
The desired time period is selected.,Event
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Event
They are used for different sets of rules.,Event
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Event
The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.,Event
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",Event
The single arrow is activated.,Event
The desired time period is chosen.,Event
The POS column indicates rule precedence.,Event
This icon is deactivated when using the Bank Basket configuration.,Event
The product types in the group can be altered.,Event
No Currency Couple Group was defined for AUD and a specific bank basket should be used for all AUD requests.,Event
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Event
The logo is a green rectangle with rounded corners.,Event
This activates the single arrow.,Event
Currencies can be added or removed from the group without editing the rules themselves.,Event
The text is presented in a stylized font.,Event
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",Event
The rules shown in the figure below can be defined.,Event
The currencies configured for the group are viewed.,Event
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,Event
The enhancement provides improved rule management capabilities.,Event
Rule 1 defines the Bank Basket if a request meets its criteria.,Event
The new currency must be selected by the user.,Event
"To view the currencies configured for the group, click on the Currency Group name.",Event
A predefined Configuration group is not needed.,Event
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",Event
A user clicks Create Group.,Event
A product group can be used to simplify rule creation,Event
The ISO code is chosen in the drop down list.,Event
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,Event
Only users with corresponding user rights are able to administer the company's Bank Baskets.,Event
"Bridge Administration can be accessed via the menu option ""Administration"" in the screen header of the Bridge application.",Event
Remove Group cannot be used on the Default Group.,Event
Getting started information is provided.,Event
It is not required to configure groups based on the above parameters.,Event
Bank baskets are configured.,Event
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",Event
A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.,Event
The Default Group contains all currency pairs.,Event
"Only the relevant products for RFS, Orders or SEP will apply",Event
<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.,Event
The user clicks 'Find Bank Baskets Rule'.,Event
The user finds the desired institution.,Event
Products can be added or removed from the group.,Event
The Remove Group function cannot be used on the Default Group.,Event
The toggle option is for other configuration tools.,Event
The image displays a user interface for configuring 360T Bank Baskets.,Event
"A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.",Event
The tree may include a single TEX entity.,Event
Product Groups are intended to classify product types into customized groups.,Event
The Add FX Time Period button is clicked.,Event
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,Event
Remove Group deletes an individual group.,Event
One single rule can be set for each group of products,Event
A search field will open.,Event
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible.",Event
The relationship should be rejected using the Counterpart Relationship Management tool.,Event
A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.,Event
The top of the screen contains various interface elements.,Event
360 TREASURY SYSTEMS AG holds the copyright for 2019.,Event
All Default Groups can be modified.,Event
Click Save,Event
Click Add Rule.,Event
Users should refer to the relevant user guide.,Event
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,Event
The 'Show individuals view toggle' icon is deactivated.,Event
An MM Time Period Group is created.,Event
Product Groups classify product types into customized groups,Event
Click Create Group,Event
ITEX entities are configured under the main entity.,Event
The currently active task/sheet is in the navigation panel.,Event
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",Event
Groups can be configured once and reused when creating various rules.,Event
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",Event
The selection of an institution is done by single-click within the institution tree.,Event
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Event
The introduction is presented.,Event
The bottom of the screen shows connection and time details.,Event
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,Event
pertain to,Relation
as a result,Relation
allow user to,Relation
do not affect,Relation
describes,Relation
are for,Relation
modified by removing,Relation
selected by,Relation
contains,Relation
lead to,Relation
selects,Relation
administer,Relation
not added to,Relation
at the same time,Relation
opens to,Relation
classify,Relation
includes,Relation
provides,Relation
because,Relation
configured in,Relation
used in,Relation
set,Relation
enhanced to provide,Relation
found within,Relation
is of,Relation
located in,Relation
facilitate,Relation
can set up,Relation
depicts,Relation
applies to,Relation
have,Relation
added or removed from,Relation
configured by,Relation
replaced by,Relation
apply with,Relation
allow creation of,Relation
after,Relation
edited without changing,Relation
has,Relation
configured under,Relation
can be managed within,Relation
can be altered in,Relation
allows management of,Relation
shows,Relation
before,Relation
are defined as,Relation
are used in,Relation
are based on,Relation
enable setting,Relation
based on,Relation
not included in,Relation
allow blocking of,Relation
allow classification of,Relation
useful for,Relation
allow setting,Relation
accessed via,Relation
displays,Relation
cannot be,Relation
available on,Relation
include,Relation
are configured by,Relation
simplifies,Relation
has option for,Relation
opens,Relation
is participated by,Relation
reused for,Relation
is part of,Relation
preferable for,Relation
configures,Relation
available to,Relation
cannot be used on,Relation
