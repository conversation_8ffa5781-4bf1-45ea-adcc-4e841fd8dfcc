:START_ID,:END_ID,relation,concepts,synsets,:TYPE
The introduction is presented.,Getting started information is provided.,before,[],[],Relation
Getting started information is provided.,Bank baskets are configured.,before,[],[],Relation
Bank baskets are configured.,The bank baskets evaluator tool is used.,before,[],[],Relation
The bank baskets evaluator tool is used.,Contact information for 360T is available.,before,[],[],Relation
The image shows a logo for 360T.,image,is participated by,[],[],Relation
The image shows a logo for 360T.,logo,is participated by,[],[],Relation
The image shows a logo for 360T.,360T,is participated by,[],[],Relation
The logo is a green rectangle with rounded corners.,logo,is participated by,[],[],Relation
The logo is a green rectangle with rounded corners.,green rectangle,is participated by,[],[],Relation
The logo is a green rectangle with rounded corners.,rounded corners,is participated by,[],[],Relation
The text 360 is in white with a green outline.,text,is participated by,[],[],Relation
The text 360 is in white with a green outline.,360,is participated by,[],[],Relation
The text 360 is in white with a green outline.,white,is participated by,[],[],Relation
The text 360 is in white with a green outline.,green outline,is participated by,[],[],Relation
The T is in white with a green outline.,T,is participated by,[],[],Relation
The T is in white with a green outline.,white,is participated by,[],[],Relation
The T is in white with a green outline.,green outline,is participated by,[],[],Relation
Two arrows point towards each other in the middle of the 0 in 360.,arrows,is participated by,[],[],Relation
Two arrows point towards each other in the middle of the 0 in 360.,0,is participated by,[],[],Relation
Two arrows point towards each other in the middle of the 0 in 360.,360,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,file,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,proprietary information,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,confidential information,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,trade secrets,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,information,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,third party,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,written approval,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,360 TREASURY SYSTEMS AG,is participated by,[],[],Relation
360 TREASURY SYSTEMS AG holds the copyright for 2019.,360 TREASURY SYSTEMS AG,is participated by,[],[],Relation
360 TREASURY SYSTEMS AG holds the copyright for 2019.,copyright,is participated by,[],[],Relation
360 TREASURY SYSTEMS AG holds the copyright for 2019.,2019,is participated by,[],[],Relation
user manual,Bank Basket feature,describes,[],[],Relation
Bank Basket feature,360T Bridge Administration tool,is part of,[],[],Relation
Bank Basket feature,improved rule management capabilities,provides,[],[],Relation
improved rule management capabilities,configuration groups,include,[],[],Relation
configuration groups,currency,are based on,[],[],Relation
configuration groups,currency couple,are based on,[],[],Relation
configuration groups,time period,are based on,[],[],Relation
configuration groups,product(s),are based on,[],[],Relation
improved rule management capabilities,configuration of separate baskets,include,[],[],Relation
separate baskets,request type,are configured by,[],[],Relation
This user manual describes the Bank Basket feature.,This user manual,is participated by,[],[],Relation
This user manual describes the Bank Basket feature.,Bank Basket feature,is participated by,[],[],Relation
This user manual describes the Bank Basket feature.,360T Bridge Administration tool,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,Bank Basket feature,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,rule management capabilities,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,configuration groups,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,currency,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,currency couple,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,time period,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,product(s),is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,separate baskets,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,request type,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,RFS,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,Order,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,SEP,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,temporary blocks,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,configured rules,is participated by,[],[],Relation
The Bank Basket feature has been enhanced to provide improved rule management capabilities.,counterpart relationship(s),is participated by,[],[],Relation
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,360T enhanced Bank Basket feature,is participated by,[],[],Relation
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,RFS request types,is participated by,[],[],Relation
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,order request types,is participated by,[],[],Relation
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,entities,is participated by,[],[],Relation
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,EMS application,is participated by,[],[],Relation
The 360T enhanced Bank Basket feature for RFS and order request types are only available to entities with the EMS and Bridge applications.,Bridge applications,is participated by,[],[],Relation
Only users with corresponding user rights are able to administer the company's Bank Baskets.,users,is participated by,[],[],Relation
Only users with corresponding user rights are able to administer the company's Bank Baskets.,user rights,is participated by,[],[],Relation
Only users with corresponding user rights are able to administer the company's Bank Baskets.,company,is participated by,[],[],Relation
Only users with corresponding user rights are able to administer the company's Bank Baskets.,Bank Baskets,is participated by,[],[],Relation
<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.,<EMAIL>,is participated by,[],[],Relation
<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.,customer relationship manager,is participated by,[],[],Relation
<NAME_EMAIL> or your customer relationship manager in order to set up the relevant administrative rights.,administrative rights,is participated by,[],[],Relation
The Bank Basket configuration is found within the Bridge Administration tool.,Bank Basket configuration,is participated by,[],[],Relation
The Bank Basket configuration is found within the Bridge Administration tool.,Bridge Administration tool,is participated by,[],[],Relation
"Bridge Administration can be accessed via the menu option ""Administration"" in the screen header of the Bridge application.",Bridge Administration,is participated by,[],[],Relation
"Bridge Administration can be accessed via the menu option ""Administration"" in the screen header of the Bridge application.","menu option ""Administration""",is participated by,[],[],Relation
"Bridge Administration can be accessed via the menu option ""Administration"" in the screen header of the Bridge application.",screen header,is participated by,[],[],Relation
"Bridge Administration can be accessed via the menu option ""Administration"" in the screen header of the Bridge application.",Bridge application,is participated by,[],[],Relation
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,Bridge Administration feature,is participated by,[],[],Relation
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,homepage,is participated by,[],[],Relation
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,shortcuts,is participated by,[],[],Relation
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,configuration tools,is participated by,[],[],Relation
The Bridge Administration feature opens to a homepage with available shortcuts to different configuration tools for the particular user.,user,is participated by,[],[],Relation
A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.,quick navigation toolbar,is participated by,[],[],Relation
A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.,active homepage icon,is participated by,[],[],Relation
A quick navigation toolbar showing the active homepage icon is available on the left side of the homepage.,homepage,is participated by,[],[],Relation
Bank Baskets quick link,navigation panel,opens,[],[],Relation
navigation panel,institution tree,contains,[],[],Relation
institution tree,single TEX entity,includes,[],[],Relation
institution tree,TEX main entity,includes,[],[],Relation
TEX main entity,trade-as entities,configures,[],[],Relation
TEX main entity,trade-on-behalf entities,configures,[],[],Relation
TEX main entity,ITEX entities,configures,[],[],Relation
single-click,institution,selects,[],[],Relation
single-click,new form/sheet,opens,[],[],Relation
new form/sheet,Bank Basket configuration details,displays,[],[],Relation
Bank Basket configuration details,selected institution,pertain to,[],[],Relation
An institution is selected by a single-click.,A new form or sheet opens with the Bank Basket configuration details.,as a result,[],[],Relation
An item is selected.,The selected item is highlighted as an active task inside the taskbar.,as a result,[],[],Relation
The search icon is activated.,A search field will open.,as a result,[],[],Relation
The user types an alphanumeric value.,The user finds the desired institution.,because,[],[],Relation
The user clicks scroll from source.,Jumping to the selected tree item is possible.,as a result,[],[],Relation
The Bank Basket configuration is used.,The 'Show individuals view toggle' icon is deactivated.,because,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel.",Bank Baskets quick link,is participated by,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel.",navigation panel,is participated by,[],[],Relation
The navigation panel contains an institution tree.,navigation panel,is participated by,[],[],Relation
The navigation panel contains an institution tree.,institution tree,is participated by,[],[],Relation
The tree may include a single TEX entity.,tree,is participated by,[],[],Relation
The tree may include a single TEX entity.,TEX entity,is participated by,[],[],Relation
The tree may include a TEX main entity.,tree,is participated by,[],[],Relation
The tree may include a TEX main entity.,TEX main entity,is participated by,[],[],Relation
Trade-as entities are configured under the main entity.,trade-as entities,is participated by,[],[],Relation
Trade-as entities are configured under the main entity.,main entity,is participated by,[],[],Relation
Trade-on-behalf entities are configured under the main entity.,trade-on-behalf entities,is participated by,[],[],Relation
Trade-on-behalf entities are configured under the main entity.,main entity,is participated by,[],[],Relation
ITEX entities are configured under the main entity.,ITEX entities,is participated by,[],[],Relation
ITEX entities are configured under the main entity.,main entity,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree.,selection,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree.,institution,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree.,single-click,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree.,institution tree,is participated by,[],[],Relation
A single-click within the institution tree opens a new form/sheet.,single-click,is participated by,[],[],Relation
A single-click within the institution tree opens a new form/sheet.,institution tree,is participated by,[],[],Relation
A single-click within the institution tree opens a new form/sheet.,new form/sheet,is participated by,[],[],Relation
The new form/sheet contains the Bank Basket configuration details of that entity.,new form/sheet,is participated by,[],[],Relation
The new form/sheet contains the Bank Basket configuration details of that entity.,Bank Basket configuration details,is participated by,[],[],Relation
The new form/sheet contains the Bank Basket configuration details of that entity.,entity,is participated by,[],[],Relation
It is possible to open multiple forms/sheets at a time.,multiple forms/sheets,is participated by,[],[],Relation
The selected item will be highlighted as an active task.,selected item,is participated by,[],[],Relation
The selected item will be highlighted as an active task.,active task,is participated by,[],[],Relation
The selected item will be highlighted inside the taskbar.,selected item,is participated by,[],[],Relation
The selected item will be highlighted inside the taskbar.,taskbar,is participated by,[],[],Relation
A set of icons is placed at the top of the navigation panel.,set of icons,is participated by,[],[],Relation
A set of icons is placed at the top of the navigation panel.,navigation panel,is participated by,[],[],Relation
The icons can be activated by a single-click.,icons,is participated by,[],[],Relation
The icons can be activated by a single-click.,single-click,is participated by,[],[],Relation
The icons can be deactivated by a single-click.,icons,is participated by,[],[],Relation
The icons can be deactivated by a single-click.,single-click,is participated by,[],[],Relation
A search field will open.,search field,is participated by,[],[],Relation
The user can type in an alphanumeric value.,user,is participated by,[],[],Relation
The user can type in an alphanumeric value.,alphanumeric value,is participated by,[],[],Relation
The user can find the desired institution.,user,is participated by,[],[],Relation
The user can find the desired institution.,desired institution,is participated by,[],[],Relation
"The ""Scroll from source"" feature can be used.",Scroll from source feature,is participated by,[],[],Relation
The user desires to find the currently active task/sheet.,user,is participated by,[],[],Relation
The user desires to find the currently active task/sheet.,currently active task/sheet,is participated by,[],[],Relation
The currently active task/sheet is in the navigation panel.,currently active task/sheet,is participated by,[],[],Relation
The currently active task/sheet is in the navigation panel.,navigation panel,is participated by,[],[],Relation
Jumping to the selected tree item is possible.,selected tree item,is participated by,[],[],Relation
Jumping to the active institution in the taskbar is possible.,active institution,is participated by,[],[],Relation
Jumping to the active institution in the taskbar is possible.,taskbar,is participated by,[],[],Relation
"Jumping is possible when clicking ""scroll from source"".",scroll from source,is participated by,[],[],Relation
"The ""Show individuals view toggle"" icon is deactivated.",Show individuals view toggle icon,is participated by,[],[],Relation
"The ""Show individuals view toggle"" icon is deactivated when using the Bank Basket configuration.",Show individuals view toggle icon,is participated by,[],[],Relation
"The ""Show individuals view toggle"" icon is deactivated when using the Bank Basket configuration.",Bank Basket configuration,is participated by,[],[],Relation
The toggle option allows the user to display only individuals.,toggle option,is participated by,[],[],Relation
The toggle option allows the user to display only individuals.,user,is participated by,[],[],Relation
The toggle option allows the user to display only individuals.,individuals,is participated by,[],[],Relation
The individuals are displayed in the navigation panel.,individuals,is participated by,[],[],Relation
The individuals are displayed in the navigation panel.,navigation panel,is participated by,[],[],Relation
The toggle option is for other configuration tools.,toggle option,is participated by,[],[],Relation
The toggle option is for other configuration tools.,other configuration tools,is participated by,[],[],Relation
"The ""Show institutions view toggle"" icon is deactivated.",Show institutions view toggle icon,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Individual unsaved changes can be reverted by clicking on the arrow icon.,as a result,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",as a result,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,toggle option,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,user,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,institutions,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,navigation panel,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,configuration tools,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,navigation panel,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,minimize icon,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,panel,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,entity tab,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Live Audit Log,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,unsaved changes,is participated by,[],[],Relation
Individual unsaved changes can be reverted by clicking on the arrow icon.,unsaved changes,is participated by,[],[],Relation
Individual unsaved changes can be reverted by clicking on the arrow icon.,arrow icon,is participated by,[],[],Relation
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.","""Discard all changes"" button",is participated by,[],[],Relation
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",unsaved changes,is participated by,[],[],Relation
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,image,is participated by,[],[],Relation
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,text 'User Guide 360T Bank Baskets Configuration',is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,entity,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,institution tree,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,Configuration Groups tab,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,data tabs,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,parameters,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Bank Basket configuration,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,RFS,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Order,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,SEP,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,users,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,group,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,rules,is participated by,[],[],Relation
RFS requests can be configured specifically.,RFS requests,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS FX Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS MM Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS Commodity Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS Cross Currency Netting Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,this tab,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Provider Groups,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,temporarily Blocked Providers,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Forward orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Spot orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,this tab,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Provider Groups,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,temporarily Blocked Providers,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Supersonic (SEP),is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,SEP streaming spot executions,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,this tab,is participated by,[],[],Relation
The image shows the logo of DEUTSCHE GROUP.,image,is participated by,[],[],Relation
The image shows the logo of DEUTSCHE GROUP.,logo,is participated by,[],[],Relation
The image shows the logo of DEUTSCHE GROUP.,DEUTSCHE GROUP,is participated by,[],[],Relation
Configuration Groups,centralized management,facilitate,[],[],Relation
parameters,Bank Basket configuration,used in,[],[],Relation
Groups,each parameter,configured in,[],[],Relation
Groups,creating rules,reused for,[],[],Relation
groups,set of rules,edited without changing,[],[],Relation
available parameters,Currency Groups,include,[],[],Relation
available parameters,Currency Couple Groups,include,[],[],Relation
available parameters,FX Time Period Groups,include,[],[],Relation
available parameters,MM Time Period Groups,include,[],[],Relation
icons,create new group,allow user to,[],[],Relation
icons,edit name of existing group,allow user to,[],[],Relation
icons,delete group,allow user to,[],[],Relation
icons,save changes,allow user to,[],[],Relation
Rename Group,Default Group,cannot be used on,[],[],Relation
Remove Group,Default Group,cannot be used on,[],[],Relation
removed group,Default Group,replaced by,[],[],Relation
Configuration Groups,configuring complex bank basket rules,useful for,[],[],Relation
Users,individual custom rules,set,[],[],Relation
Individual custom rules,users with less complex bank basket setups,preferable for,[],[],Relation
Bank Basket Configuration,Default Group,contains,[],[],Relation
Default Group,existing values,includes,[],[],Relation
Default Groups,values,modified by removing,[],[],Relation
new values,Default Group,not added to,[],[],Relation
new currency,Default Currency Group,not included in,[],[],Relation
new currency,user,selected by,[],[],Relation
Currency Groups,single currencies,allow classification of,[],[],Relation
Currency Groups,single rule for group of currencies,allow setting,[],[],Relation
Currencies,group,added or removed from,[],[],Relation
currency group,rule creation for interest rate products,simplifies,[],[],Relation
interest rate products,Loan,include,[],[],Relation
interest rate products,Deposit,include,[],[],Relation
interest rate products,Interest Rate Swap,include,[],[],Relation
interest rate products,FRA,include,[],[],Relation
interest rate products,CapFloor,include,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,parameters,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Bank Basket configuration,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Groups,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",parameter,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",rules,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",requests,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",RFS,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Orders,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",SEP,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,groups,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,values,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,rules,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",parameters,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Currency Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Currency Couple Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",FX Time Period Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",MM Time Period Groups,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",icons,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",Configuration Group area,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",user,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",group,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",name,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",changes,is participated by,[],[],Relation
Create Group adds a new group.,Create Group,is participated by,[],[],Relation
Create Group adds a new group.,group,is participated by,[],[],Relation
Rename Group changes the name of a group.,Rename Group,is participated by,[],[],Relation
Rename Group changes the name of a group.,name,is participated by,[],[],Relation
Rename Group changes the name of a group.,group,is participated by,[],[],Relation
Rename Group cannot be used on the Default Group.,Rename Group,is participated by,[],[],Relation
Rename Group cannot be used on the Default Group.,Default Group,is participated by,[],[],Relation
Remove Group deletes an individual group.,Remove Group,is participated by,[],[],Relation
Remove Group deletes an individual group.,group,is participated by,[],[],Relation
Remove Group cannot be used on the Default Group.,Remove Group,is participated by,[],[],Relation
Remove Group cannot be used on the Default Group.,Default Group,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,removed group,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,rules,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,Default Group,is participated by,[],[],Relation
Users are reminded to save changes to their configurations.,users,is participated by,[],[],Relation
Users are reminded to save changes to their configurations.,changes,is participated by,[],[],Relation
Users are reminded to save changes to their configurations.,configurations,is participated by,[],[],Relation
Configuration Groups are particularly useful when configuring complex bank basket rules.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups are particularly useful when configuring complex bank basket rules.,bank basket rules,is participated by,[],[],Relation
Tri Party Repo found in the RFS MM Bank Baskets area.,Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.,at the same time,[],[],Relation
A default group exists which includes all currencies.,This group cannot be removed or renamed.,at the same time,[],[],Relation
A default group exists which includes all currencies.,The currencies in the group can be altered as described below.,at the same time,[],[],Relation
This group cannot be removed or renamed.,The currencies in the group can be altered as described below.,at the same time,[],[],Relation
A currency is highlighted with a single-click.,The single arrow is activated.,as a result,[],[],Relation
The single arrow is activated.,Clicking the single arrow moves the desired currency.,before,[],[],Relation
A user clicks Create Group.,A user types the desired name.,before,[],[],Relation
A user types the desired name.,A user clicks Create Group again.,before,[],[],Relation
A user clicks Create Group again.,A user clicks Save.,before,[],[],Relation
A user clicks on the Currency Group name.,The currencies configured for the group are viewed.,as a result,[],[],Relation
A Currency Couple Group is created.,The Currency Couple Group can be used to simplify rules.,as a result,[],[],Relation
Currencies may be added or removed from the default group.,Currencies,is participated by,[],[],Relation
Currencies may be added or removed from the default group.,default group,is participated by,[],[],Relation
A currency is highlighted with a single-click.,A currency,is participated by,[],[],Relation
A currency is highlighted with a single-click.,single-click,is participated by,[],[],Relation
Highlighting a currency activates the single arrow.,Highlighting a currency,is participated by,[],[],Relation
Highlighting a currency activates the single arrow.,single arrow,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,single arrow,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,desired currency,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Available,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Selected,is participated by,[],[],Relation
All currencies can be moved in either direction by using the double arrows.,All currencies,is participated by,[],[],Relation
All currencies can be moved in either direction by using the double arrows.,double arrows,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",new group,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Create Group,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",desired name,is participated by,[],[],Relation
"A new group can be added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Save,is participated by,[],[],Relation
Currencies configured for the group can be viewed by clicking on the Currency Group name.,currencies,is participated by,[],[],Relation
Currencies configured for the group can be viewed by clicking on the Currency Group name.,group,is participated by,[],[],Relation
Currencies configured for the group can be viewed by clicking on the Currency Group name.,Currency Group name,is participated by,[],[],Relation
The same currency can be added to many different groups.,currency,is participated by,[],[],Relation
The same currency can be added to many different groups.,groups,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,system,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,creation of groups,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,overlapping sets of currencies,is participated by,[],[],Relation
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",Currency Couple Groups,is participated by,[],[],Relation
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",buckets of currency pairs,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Currency Couple Group,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",rules,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",FX products,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",RFS FX Bank Baskets area,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",FX Spot,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Forwards,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Swaps,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",NDF,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",NDS,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Options,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Block Trades,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Energy Asian Swaps,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Bullet Swaps,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",RFS Commodity Bank Baskets area,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Order Spot,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Forwards,is participated by,[],[],Relation
"A Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and Order Spot and Forwards found in the Orders Bank Basket area.",Orders Bank Basket area,is participated by,[],[],Relation
The introduction is presented.,Getting started information is provided.,before,[],[],Relation
Getting started information is provided.,Bank baskets are configured.,before,[],[],Relation
Bank baskets are configured.,The bank baskets evaluator tool is used.,before,[],[],Relation
The bank baskets evaluator tool is used.,Contact information for 360T is available.,before,[],[],Relation
The image shows a logo for 360T.,image,is participated by,[],[],Relation
The image shows a logo for 360T.,logo,is participated by,[],[],Relation
The image shows a logo for 360T.,360T,is participated by,[],[],Relation
The logo is a green rectangle with rounded corners.,logo,is participated by,[],[],Relation
The logo is a green rectangle with rounded corners.,green rectangle,is participated by,[],[],Relation
The logo is a green rectangle with rounded corners.,rounded corners,is participated by,[],[],Relation
The text 360 is in white with a green outline.,text,is participated by,[],[],Relation
The text 360 is in white with a green outline.,360,is participated by,[],[],Relation
The text 360 is in white with a green outline.,white,is participated by,[],[],Relation
The text 360 is in white with a green outline.,green outline,is participated by,[],[],Relation
The T is in white with a green outline.,T,is participated by,[],[],Relation
The T is in white with a green outline.,white,is participated by,[],[],Relation
The T is in white with a green outline.,green outline,is participated by,[],[],Relation
Two arrows point towards each other in the middle of the 0 in 360.,arrows,is participated by,[],[],Relation
Two arrows point towards each other in the middle of the 0 in 360.,0,is participated by,[],[],Relation
Two arrows point towards each other in the middle of the 0 in 360.,360,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,file,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,proprietary information,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,confidential information,is participated by,[],[],Relation
This file contains proprietary and confidential information including trade secrets.,trade secrets,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,information,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,third party,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,written approval,is participated by,[],[],Relation
The information may not be divulged to any third party without prior written approval from 360 TREASURY SYSTEMS AG.,360 TREASURY SYSTEMS AG,is participated by,[],[],Relation
360 TREASURY SYSTEMS AG holds the copyright for 2019.,360 TREASURY SYSTEMS AG,is participated by,[],[],Relation
360 TREASURY SYSTEMS AG holds the copyright for 2019.,copyright,is participated by,[],[],Relation
360 TREASURY SYSTEMS AG holds the copyright for 2019.,2019,is participated by,[],[],Relation
user manual,Bank Basket feature,describes,[],[],Relation
Bank Basket feature,360T Bridge Administration tool,is part of,[],[],Relation
Bank Basket feature,improved rule management capabilities,enhanced to provide,[],[],Relation
improved rule management capabilities,configuration groups,include,[],[],Relation
configuration groups,currency,based on,[],[],Relation
configuration groups,currency couple,based on,[],[],Relation
configuration groups,time period,based on,[],[],Relation
configuration groups,product(s),based on,[],[],Relation
improved rule management capabilities,configuration of separate baskets,include,[],[],Relation
separate baskets,"request type (RFS, Order, SEP)",configured by,[],[],Relation
improved rule management capabilities,ability to apply and remove temporary blocks,include,[],[],Relation
temporary blocks,configured rules,do not affect,[],[],Relation
temporary blocks,counterpart relationship(s),do not affect,[],[],Relation
360T enhanced Bank Basket feature,entities,available to,[],[],Relation
entities,EMS application,have,[],[],Relation
entities,Bridge application,have,[],[],Relation
users,corresponding user rights,have,[],[],Relation
users,company's Bank Baskets,administer,[],[],Relation
<EMAIL>,relevant administrative rights,can set up,[],[],Relation
customer relationship manager,relevant administrative rights,can set up,[],[],Relation
Bank Basket configuration,Bridge Administration tool,found within,[],[],Relation
Bridge Administration,"menu option ""Administration""",accessed via,[],[],Relation
"menu option ""Administration""",screen header,located in,[],[],Relation
screen header,Bridge application,is part of,[],[],Relation
Bridge Administration feature,homepage,opens to,[],[],Relation
homepage,available shortcuts,has,[],[],Relation
available shortcuts,different configuration tools,lead to,[],[],Relation
different configuration tools,particular user,are for,[],[],Relation
quick navigation toolbar,active homepage icon,shows,[],[],Relation
quick navigation toolbar,left side of the homepage,available on,[],[],Relation
image,screenshot,shows,[],[],Relation
screenshot,360T Bank Baskets Configuration user guide,is of,[],[],Relation
screen,'Administration Start' page,displays,[],[],Relation
'Administration Start' page,'Regulatory Data',has option for,[],[],Relation
'Administration Start' page,'Bank Baskets',has option for,[],[],Relation
'Administration Start' page,'Change Request',has option for,[],[],Relation
'Administration Start' page,'Wizards',has option for,[],[],Relation
'Administration Start' page,'Evaluator Tools',has option for,[],[],Relation
top of the screen,tabs,includes,[],[],Relation
tabs,'RFS REQUESTER',include,[],[],Relation
tabs,'DEAL TRACKING',include,[],[],Relation
tabs,'BRIDGE ADMINISTRATION',include,[],[],Relation
top of the screen,preferences options,includes,[],[],Relation
top of the screen,help options,includes,[],[],Relation
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,user manual,is participated by,[],[],Relation
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,Bank Basket feature,is participated by,[],[],Relation
This user manual describes the Bank Basket feature of the 360T Bridge Administration tool.,360T Bridge Administration tool,is participated by,[],[],Relation
The Bank Basket feature has been enhanced.,Bank Basket feature,is participated by,[],[],Relation
The enhancement provides improved rule management capabilities.,enhancement,is participated by,[],[],Relation
The enhancement provides improved rule management capabilities.,rule management capabilities,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",configuration groups,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",currency,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",currency couple,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",time period,is participated by,[],[],Relation
"Configuration groups based on currency, currency couple, time period and product(s) have been introduced.",product(s),is participated by,[],[],Relation
Separate baskets can be configured by request type.,separate baskets,is participated by,[],[],Relation
Separate baskets can be configured by request type.,request type,is participated by,[],[],Relation
Request types include RFS.,RFS,is participated by,[],[],Relation
Request types include RFS.,request types,is participated by,[],[],Relation
Request types include Order.,Order,is participated by,[],[],Relation
Request types include Order.,request types,is participated by,[],[],Relation
Request types include SEP.,SEP,is participated by,[],[],Relation
Request types include SEP.,request types,is participated by,[],[],Relation
Temporary blocks can be applied.,temporary blocks,is participated by,[],[],Relation
Temporary blocks can be removed.,temporary blocks,is participated by,[],[],Relation
Applying temporary blocks does not affect the configured rules.,temporary blocks,is participated by,[],[],Relation
Applying temporary blocks does not affect the configured rules.,configured rules,is participated by,[],[],Relation
Figure 2,Bridge Administration Homepage,depicts,[],[],Relation
"""Bank Baskets"" quick link",navigation panel,opens,[],[],Relation
navigation panel,institution tree,contains,[],[],Relation
institution tree,single TEX entity,includes,[],[],Relation
institution tree,TEX main entity,includes,[],[],Relation
trade-as entities,TEX main entity,configured under,[],[],Relation
trade-on-behalf entities,TEX main entity,configured under,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",Bank Baskets quick link,is participated by,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",navigation panel,is participated by,[],[],Relation
"The ""Bank Baskets"" quick link opens a navigation panel which contains an institution tree.",institution tree,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",tree,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",TEX entity,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",TEX main entity,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",trade-as,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",trade-on-behalf,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",ITEX entities,is participated by,[],[],Relation
"Depending on the setup, the tree may include a single TEX entity or a TEX main entity with trade-as, trade-on-behalf or ITEX entities configured under the main entity.",main entity,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,institution,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,single-click,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,institution tree,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,new form/sheet,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,Bank Basket configuration details,is participated by,[],[],Relation
The selection of an institution is done by single-click within the institution tree which opens a new form/sheet with the Bank Basket configuration details of that entity.,entity,is participated by,[],[],Relation
It is possible to open multiple forms/sheets at a time.,multiple forms/sheets,is participated by,[],[],Relation
The selected item will be highlighted as an active task inside the taskbar.,selected item,is participated by,[],[],Relation
The selected item will be highlighted as an active task inside the taskbar.,active task,is participated by,[],[],Relation
The selected item will be highlighted as an active task inside the taskbar.,taskbar,is participated by,[],[],Relation
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,set of icons,is participated by,[],[],Relation
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,single-click,is participated by,[],[],Relation
A set of icons which can be activated and deactivated by a single-click is placed at the top of the navigation panel:,navigation panel,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,search field,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,user,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,alphanumeric value,is participated by,[],[],Relation
A search field will open and the user can type in an alphanumeric value in order to find the desired institution.,institution,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,This feature,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,user,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,active task/sheet,is participated by,[],[],Relation
This feature can be used in the event that the user desires to find the currently active task/sheet in the navigation panel.,navigation panel,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,selected tree item,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,active institution,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,taskbar,is participated by,[],[],Relation
Jumping to the selected tree item (active institution in the taskbar) is possible when clicking scroll from source.,scroll from source,is participated by,[],[],Relation
This icon is deactivated when using the Bank Basket configuration.,This icon,is participated by,[],[],Relation
This icon is deactivated when using the Bank Basket configuration.,Bank Basket configuration,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,other configuration tools,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,toggle option,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,user,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,individuals,is participated by,[],[],Relation
For other configuration tools the toggle option allows the user to display only individuals in the navigation panel.,navigation panel,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Individual unsaved changes can be reverted by clicking on the arrow icon.,as a result,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",as a result,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,toggle option,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,user,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,institutions,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,navigation panel,is participated by,[],[],Relation
The toggle option allows the user to display only institutions in the navigation panel.,configuration tools,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,navigation panel,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,minimize icon,is participated by,[],[],Relation
The navigation panel can be minimized by clicking on the minimize icon in the upper right corner of the panel.,panel,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,entity tab,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,Live Audit Log,is participated by,[],[],Relation
Each entity tab has a Live Audit Log which tracks all unsaved changes.,unsaved changes,is participated by,[],[],Relation
Individual unsaved changes can be reverted by clicking on the arrow icon.,unsaved changes,is participated by,[],[],Relation
Individual unsaved changes can be reverted by clicking on the arrow icon.,arrow icon,is participated by,[],[],Relation
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.","""Discard all changes"" button",is participated by,[],[],Relation
"Clicking on the ""Discard all changes"" button will revert all unsaved changes.",unsaved changes,is participated by,[],[],Relation
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,image,is participated by,[],[],Relation
The image contains the text 'User Guide 360T Bank Baskets Configuration'.,text 'User Guide 360T Bank Baskets Configuration',is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,entity,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,institution tree,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,Configuration Groups tab,is participated by,[],[],Relation
Selecting an entity from the institution tree displays a Configuration Groups tab with additional data tabs.,data tabs,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,parameters,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Bank Basket configuration,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,RFS,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,Order,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters that can be used in each Bank Basket configuration.,SEP,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,users,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,group,is participated by,[],[],Relation
Configuration Groups allow users to create a group one single time and reuse it across various rules.,rules,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,request types,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,this tab,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS FX Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS MM Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS Commodity Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for four separate request types are configured on this tab.,RFS Cross Currency Netting Bank Baskets,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Provider Groups,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Blocked Providers,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Orders,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Orders.,Order Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Forward orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,Spot orders,is participated by,[],[],Relation
Bank Basket rules for Forward or Spot orders are configured on this tab.,this tab,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Provider Groups,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Blocked Providers,is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,Supersonic (SEP),is participated by,[],[],Relation
Provider Groups and temporarily Blocked Providers may be specifically configured for Supersonic (SEP).,SEP Bank Baskets,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,Bank Basket rules,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,SEP streaming spot executions,is participated by,[],[],Relation
Bank Basket rules for SEP streaming spot executions are configured on this tab.,this tab,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",Bank Basket Configurations,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",RFS requests,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",Orders requests,is participated by,[],[],Relation
"Bank Basket Configurations for RFS, Orders and SEP requests are separate and independent of one another.",SEP requests,is participated by,[],[],Relation
An RFS configuration will have no impact on SEP trading.,RFS configuration,is participated by,[],[],Relation
An RFS configuration will have no impact on SEP trading.,SEP trading,is participated by,[],[],Relation
Configuration Groups,centralized management,facilitate,[],[],Relation
parameters,Bank Basket configuration,used in,[],[],Relation
Groups,each parameter,configured in,[],[],Relation
Groups,creating rules,reused for,[],[],Relation
groups,set of rules,edited without changing,[],[],Relation
available parameters,Currency Groups,include,[],[],Relation
available parameters,Currency Couple Groups,include,[],[],Relation
available parameters,FX Time Period Groups,include,[],[],Relation
available parameters,MM Time Period Groups,include,[],[],Relation
icons,create new group,allow user to,[],[],Relation
icons,edit name of existing group,allow user to,[],[],Relation
icons,delete group,allow user to,[],[],Relation
icons,save changes,allow user to,[],[],Relation
Rename Group,Default Group,cannot be used on,[],[],Relation
Remove Group,Default Group,cannot be used on,[],[],Relation
removed group,Default Group,replaced by,[],[],Relation
Configuration Groups,configuring complex bank basket rules,useful for,[],[],Relation
Users,individual custom rules,set,[],[],Relation
Individual custom rules,users with less complex bank basket setups,preferable for,[],[],Relation
Bank Basket Configuration,Default Group,contains,[],[],Relation
Default Group,existing values,includes,[],[],Relation
Default Groups,values,modified by removing,[],[],Relation
new values,Default Group,not added to,[],[],Relation
new currency,Default Currency Group,not included in,[],[],Relation
new currency,user,selected by,[],[],Relation
Currency Groups,single currencies,allow classification of,[],[],Relation
Currency Groups,single rule for group of currencies,allow setting,[],[],Relation
Currencies,group,added or removed from,[],[],Relation
currency group,rule creation for interest rate products,simplifies,[],[],Relation
interest rate products,Loan,include,[],[],Relation
interest rate products,Deposit,include,[],[],Relation
interest rate products,Interest Rate Swap,include,[],[],Relation
interest rate products,FRA,include,[],[],Relation
interest rate products,CapFloor,include,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Groups can be configured once and reused when creating various rules.,as a result,[],[],Relation
Groups can be configured once and reused when creating various rules.,The groups themselves can be edited without changing a set of rules based on those groups.,at the same time,[],[],Relation
The Rename Group function is to change the name of a group.,The Rename Group function cannot be used on the Default Group.,at the same time,[],[],Relation
The Remove Group function is to delete an individual group.,The Remove Group function cannot be used on the Default Group.,at the same time,[],[],Relation
A group is removed.,The removed group is replaced by the Default Group if it is used in any configured rules.,as a result,[],[],Relation
It is not required to configure groups based on the above parameters.,Users may still set individual custom rules without utilizing the Configuration Groups.,as a result,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,centralized management,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,parameters,is participated by,[],[],Relation
Configuration Groups facilitate centralized management of parameters.,Bank Basket configuration,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Groups,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",parameters,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",rules,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",requests,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",RFS,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",Orders,is participated by,[],[],Relation
"Groups in each parameter can be configured once and then reused when creating various rules for requests executed via RFS, Orders or SEP.",SEP,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,groups,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,values,is participated by,[],[],Relation
The groups themselves can be edited without changing a set of rules based on those groups.,rules,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",parameters,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Currency Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",Currency Couple Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",FX Time Period Groups,is participated by,[],[],Relation
"The available parameters are Currency Groups, Currency Couple Groups, FX Time Period Groups, and MM Time Period Groups.",MM Time Period Groups,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",icons,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",Configuration Group area,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",user,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",group,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",name,is participated by,[],[],Relation
"A set of icons appear in each Configuration Group area allow the user to create a new group, edit the name of an existing group, delete a group or save changes.",changes,is participated by,[],[],Relation
Create Group adds a new group.,Create Group,is participated by,[],[],Relation
Create Group adds a new group.,group,is participated by,[],[],Relation
Rename Group changes the name of a group.,Rename Group,is participated by,[],[],Relation
Rename Group changes the name of a group.,name,is participated by,[],[],Relation
Rename Group changes the name of a group.,group,is participated by,[],[],Relation
Rename Group cannot be used on the Default Group.,Rename Group,is participated by,[],[],Relation
Rename Group cannot be used on the Default Group.,Default Group,is participated by,[],[],Relation
Remove Group deletes an individual group.,Remove Group,is participated by,[],[],Relation
Remove Group deletes an individual group.,group,is participated by,[],[],Relation
Remove Group cannot be used on the Default Group.,Remove Group,is participated by,[],[],Relation
Remove Group cannot be used on the Default Group.,Default Group,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,removed group,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,configured rules,is participated by,[],[],Relation
If the removed group is used in any configured rules this group is replaced by the Default Group.,Default Group,is participated by,[],[],Relation
Users are reminded to save changes to configurations.,users,is participated by,[],[],Relation
Users are reminded to save changes to configurations.,changes,is participated by,[],[],Relation
Users are reminded to save changes to configurations.,configurations,is participated by,[],[],Relation
Configuration Groups are particularly useful when configuring complex bank basket rules.,Configuration Groups,is participated by,[],[],Relation
Configuration Groups are particularly useful when configuring complex bank basket rules.,complex bank basket rules,is participated by,[],[],Relation
It is not required to configure groups based on the above parameters.,groups,is participated by,[],[],Relation
It is not required to configure groups based on the above parameters.,parameters,is participated by,[],[],Relation
Users may still set individual custom rules without utilizing the Configuration Groups.,Users,is participated by,[],[],Relation
Users may still set individual custom rules without utilizing the Configuration Groups.,individual custom rules,is participated by,[],[],Relation
Users may still set individual custom rules without utilizing the Configuration Groups.,Configuration Groups,is participated by,[],[],Relation
Individual custom rules may be preferable for some users with less complex bank basket setups.,Individual custom rules,is participated by,[],[],Relation
Individual custom rules may be preferable for some users with less complex bank basket setups.,users,is participated by,[],[],Relation
Individual custom rules may be preferable for some users with less complex bank basket setups.,bank basket setups,is participated by,[],[],Relation
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",Bank Basket Configuration,is participated by,[],[],Relation
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",parameters,is participated by,[],[],Relation
"Upon the initial activation of the Bank Basket Configuration, each of the parameters will automatically contain a Default Group.",Default Group,is participated by,[],[],Relation
The Default Group will include all existing values.,Default Group,is participated by,[],[],Relation
The Default Group will include all existing values.,existing values,is participated by,[],[],Relation
All Default Groups can be modified.,Default Groups,is participated by,[],[],Relation
All Default Groups can be modified.,values,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",tool,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",values,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",versions,is participated by,[],[],Relation
"In case the tool is enhanced with additional possible values in later versions, the new values will not be added to the Default Group.",Default Group,is participated by,[],[],Relation
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,new currency,is participated by,[],[],Relation
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,360T platform,is participated by,[],[],Relation
If a new currency is added to the 360T platform the Default Currency Group will not include the new currency.,Default Currency Group,is participated by,[],[],Relation
The new currency must be selected by the user.,new currency,is participated by,[],[],Relation
The new currency must be selected by the user.,user,is participated by,[],[],Relation
Currency Groups are intended to allow the classification of single currencies into customized groups.,Currency Groups,is participated by,[],[],Relation
Currency Groups are intended to allow the classification of single currencies into customized groups.,single currencies,is participated by,[],[],Relation
Currency Groups are intended to allow the classification of single currencies into customized groups.,customized groups,is participated by,[],[],Relation
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,rule,is participated by,[],[],Relation
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,group of currencies,is participated by,[],[],Relation
This allows setting one single rule for each group of currencies rather than many rules for individual currencies.,individual currencies,is participated by,[],[],Relation
Currencies can be added or removed from the group without editing the rules themselves.,Currencies,is participated by,[],[],Relation
Currencies can be added or removed from the group without editing the rules themselves.,group,is participated by,[],[],Relation
Currencies can be added or removed from the group without editing the rules themselves.,rules,is participated by,[],[],Relation
The image shows a user interface for managing currency groups in a financial application.,image,is participated by,[],[],Relation
The image shows a user interface for managing currency groups in a financial application.,user,is participated by,[],[],Relation
Tri Party Repo found in the RFS MM Bank Baskets area.,Cross Currency Portfolios found in the RFS Cross Currency Netting Bank Baskets area.,at the same time,[],[],Relation
A default group exists which includes all currencies.,This group cannot be removed or renamed.,at the same time,[],[],Relation
A default group exists which includes all currencies.,The currencies in the group can be altered as described below.,at the same time,[],[],Relation
This group cannot be removed or renamed.,The currencies in the group can be altered as described below.,at the same time,[],[],Relation
A currency is highlighted with a single-click.,The single arrow is activated.,as a result,[],[],Relation
The single arrow is activated.,Clicking the single arrow moves the desired currency.,before,[],[],Relation
A user clicks Create Group.,A user types the desired name.,before,[],[],Relation
A user types the desired name.,A user clicks Create Group again.,before,[],[],Relation
A user clicks Create Group again.,A user clicks Save.,before,[],[],Relation
A user clicks on the Currency Group name.,The currencies configured for the group are viewed.,as a result,[],[],Relation
A Currency Couple Group is created.,The Currency Couple Group can be used to simplify rules.,as a result,[],[],Relation
Currencies may be added or removed from the default group.,Currencies,is participated by,[],[],Relation
Currencies may be added or removed from the default group.,default group,is participated by,[],[],Relation
A currency is highlighted with a single-click.,A currency,is participated by,[],[],Relation
A currency is highlighted with a single-click.,single-click,is participated by,[],[],Relation
This activates the single arrow.,This,is participated by,[],[],Relation
This activates the single arrow.,single arrow,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,single arrow,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,desired currency,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Available,is participated by,[],[],Relation
Clicking the single arrow moves the desired currency from Available to Selected or from Selected to Available.,Selected,is participated by,[],[],Relation
All currencies can be moved in either direction by using the double arrows.,All currencies,is participated by,[],[],Relation
All currencies can be moved in either direction by using the double arrows.,double arrows,is participated by,[],[],Relation
Click Create Group.,Create Group,is participated by,[],[],Relation
Type the desired name.,desired name,is participated by,[],[],Relation
Click Create Group again.,Create Group,is participated by,[],[],Relation
Click Save.,Save,is participated by,[],[],Relation
Provide a name for a new group.,name,is participated by,[],[],Relation
Provide a name for a new group.,new group,is participated by,[],[],Relation
"To view the currencies configured for the group, click on the Currency Group name.",currencies,is participated by,[],[],Relation
"To view the currencies configured for the group, click on the Currency Group name.",group,is participated by,[],[],Relation
"To view the currencies configured for the group, click on the Currency Group name.",Currency Group name,is participated by,[],[],Relation
The same currency can be added to many different groups.,same currency,is participated by,[],[],Relation
The same currency can be added to many different groups.,many different groups,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,system,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,creation of groups,is participated by,[],[],Relation
The system does not restrict the creation of groups with overlapping sets of currencies.,overlapping sets of currencies,is participated by,[],[],Relation
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",Currency Couple Groups,is participated by,[],[],Relation
"Currency Couple Groups allow the creation of ""buckets"" of currency pairs.",buckets of currency pairs,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Currency Couple Group,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",rules,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",FX products,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",RFS FX Bank Baskets area,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",FX Spot,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Forwards,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Swaps,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",NDF,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",NDS,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Options,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Block Trades,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Energy Asian and Bullet Swaps,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",RFS Commodity Bank Baskets area,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Order Spot,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Forwards,is participated by,[],[],Relation
"Once created, a Currency Couple Group can be used to simplify rules for FX products found in the RFS FX Bank Baskets area including: FX Spot, Forwards, Swaps, NDF, NDS, Options and Block Trades, Energy Asian and Bullet Swaps in the RFS Commodity Bank Baskets area, and also Order Spot and Forwards found in the Orders Bank Basket area.",Orders Bank Basket area,is participated by,[],[],Relation
The Default Group contains all currency pairs.,Default Group,is participated by,[],[],Relation
The Default Group contains all currency pairs.,currency pairs,is participated by,[],[],Relation
The Default Group contains all currency pairs.,base currency,is participated by,[],[],Relation
The Default Group contains all currency pairs.,quote currency,is participated by,[],[],Relation
Currency Couple Groups can be created.,Currency Couple Groups,is participated by,[],[],Relation
Currency Couple Groups can be created.,Configuration Group icons,is participated by,[],[],Relation
Currency Couple Groups can be renamed.,Currency Couple Groups,is participated by,[],[],Relation
Currency Couple Groups can be renamed.,Configuration Group icons,is participated by,[],[],Relation
Currency Couple Groups can be removed.,Currency Couple Groups,is participated by,[],[],Relation
Currency Couple Groups can be removed.,Configuration Group icons,is participated by,[],[],Relation
A user can add currency pairs within a group.,user,is participated by,[],[],Relation
A user can add currency pairs within a group.,group,is participated by,[],[],Relation
A user can add currency pairs within a group.,currency pairs,is participated by,[],[],Relation
The Add Currency Couple button is clicked.,Add Currency Couple button,is participated by,[],[],Relation
The ISO code is chosen in the drop down list.,ISO code,is participated by,[],[],Relation
The ISO code is chosen in the drop down list.,drop down list,is participated by,[],[],Relation
The desired currency is typed.,desired currency,is participated by,[],[],Relation
The selection is confirmed by clicking the green check mark.,selection,is participated by,[],[],Relation
The selection is confirmed by clicking the green check mark.,green check mark,is participated by,[],[],Relation
Save is clicked.,Save,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,Products,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,maturities,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,tenors,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,maturity ranges,is participated by,[],[],Relation
Products with varying maturities or tenors may be configured into maturity ranges.,FX Time Period Groups,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,FX Time Period Group,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,rules,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,FX products,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,RFS FX Bank Baskets area,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,Forwards,is participated by,[],[],Relation
An FX Time Period Group can simplify rules for FX products.,Swaps,is participated by,[],[],Relation
The Add FX Time Period button is clicked.,The desired time period is chosen.,before,[],[],Relation
The desired time period is chosen.,The selection is confirmed by clicking the green check mark.,before,[],[],Relation
The selection is confirmed by clicking the green check mark.,The Save button is clicked.,before,[],[],Relation
The Add MM Time Period button is clicked.,The desired time period is selected.,before,[],[],Relation
The desired time period is selected.,The selection is confirmed by clicking the green check mark.,before,[],[],Relation
An MM Time Period Group is created.,Rules for interest rate products can be simplified.,as a result,[],[],Relation
The same tenors are used in various groups.,They are used for different sets of rules.,because,[],[],Relation
Tenors,range of maturities,are defined as,[],[],Relation
Tenors,start and end values,include,[],[],Relation
Tenors,various groups,are used in,[],[],Relation
Product Groups,product types,classify,[],[],Relation
Product Groups,single rule,enable setting,[],[],Relation
Products,product group,can be managed within,[],[],Relation
Product group,rule creation,simplifies,[],[],Relation
Rule creation,relevant product types,applies to,[],[],Relation
Default group,all products,includes,[],[],Relation
Default group,removed,cannot be,[],[],Relation
Default group,renamed,cannot be,[],[],Relation
Product types,Default Group,can be altered in,[],[],Relation
Relevant products,default Product Group,apply with,[],[],Relation
Bank Basket areas,Provider Groups,allow creation of,[],[],Relation
Bank Basket areas,providers,allow blocking of,[],[],Relation
User interface,RFS Requester option,includes,[],[],Relation
User interface,Deal Tracking option,includes,[],[],Relation
User interface,Bridge Administration option,includes,[],[],Relation
User interface,currency groups,allows management of,[],[],Relation
User interface,FX time period groups,allows management of,[],[],Relation
User interface,MM time period groups,allows management of,[],[],Relation
User interface,product groups,allows management of,[],[],Relation
"""Select Member for 'FX Spot and Forward'"" section",available product types,contains,[],[],Relation
"""Select Member for 'FX Spot and Forward'"" section",selected product types,contains,[],[],Relation
Product Groups classify product types into customized groups,One single rule can be set for each group of products,because,[],[],Relation
A product group is created,A product group can be used to simplify rule creation,before,[],[],Relation
Click Create Group,Type the desired name,before,[],[],Relation
Type the desired name,Click Create Group again,before,[],[],Relation
Click Create Group again,Click Save,before,[],[],Relation
A default group exists which includes all products,This default group cannot be removed or renamed,as a result,[],[],Relation
A rule utilizes the default Product Group,"Only the relevant products for RFS, Orders or SEP will apply",as a result,[],[],Relation
Periods can be added for specific durations.,periods,is participated by,[],[],Relation
Periods can be added for specific durations.,OVERNIGHT,is participated by,[],[],Relation
Periods can be added for specific durations.,1 WEEK,is participated by,[],[],Relation
Periods can be added for specific durations.,1 MONTH,is participated by,[],[],Relation
Periods can be added for specific durations.,6 MONTHS,is participated by,[],[],Relation
Tenors are defined as a range of maturities.,Tenors,is participated by,[],[],Relation
Tenors are defined as a range of maturities.,range of maturities,is participated by,[],[],Relation
Tenors are defined as a range of maturities.,start values,is participated by,[],[],Relation
Tenors are defined as a range of maturities.,end values,is participated by,[],[],Relation
The same tenors can be used in various groups for different sets of rules.,tenors,is participated by,[],[],Relation
The same tenors can be used in various groups for different sets of rules.,groups,is participated by,[],[],Relation
The same tenors can be used in various groups for different sets of rules.,sets of rules,is participated by,[],[],Relation
Product Groups are intended to classify product types into customized groups.,Product Groups,is participated by,[],[],Relation
Product Groups are intended to classify product types into customized groups.,product types,is participated by,[],[],Relation
Product Groups are intended to classify product types into customized groups.,customized groups,is participated by,[],[],Relation
This allows setting a single rule for each group of products.,rule,is participated by,[],[],Relation
This allows setting a single rule for each group of products.,group of products,is participated by,[],[],Relation
This allows setting a single rule for each group of products.,product types,is participated by,[],[],Relation
The image displays a user interface for configuring 360T Bank Baskets.,image,is participated by,[],[],Relation
The image displays a user interface for configuring 360T Bank Baskets.,user interface,is participated by,[],[],Relation
The image displays a user interface for configuring 360T Bank Baskets.,360T Bank Baskets,is participated by,[],[],Relation
"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.",user interface,is participated by,[],[],Relation
"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.",RFS Requester,is participated by,[],[],Relation
"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.",Deal Tracking,is participated by,[],[],Relation
"The user interface includes options for RFS Requester, Deal Tracking, and Bridge Administration.",Bridge Administration,is participated by,[],[],Relation
The interface enables users to manage various groups.,interface,is participated by,[],[],Relation
The interface enables users to manage various groups.,users,is participated by,[],[],Relation
The interface enables users to manage various groups.,currency groups,is participated by,[],[],Relation
The interface enables users to manage various groups.,FX time period groups,is participated by,[],[],Relation
The interface enables users to manage various groups.,MM time period groups,is participated by,[],[],Relation
The interface enables users to manage various groups.,product groups,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible.",section,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible.",FX Spot and Forward,is participated by,[],[],Relation
"A section titled ""Select Member for 'FX Spot and Forward'"" is visible.",product types,is participated by,[],[],Relation
Products can be added or removed from the group.,Products,is participated by,[],[],Relation
Products can be added or removed from the group.,group,is participated by,[],[],Relation
Products can be added or removed from the group.,rules,is participated by,[],[],Relation
A product group can simplify rule creation for relevant product types.,product group,is participated by,[],[],Relation
A product group can simplify rule creation for relevant product types.,rule creation,is participated by,[],[],Relation
A product group can simplify rule creation for relevant product types.,product types,is participated by,[],[],Relation
A product group can simplify rule creation for relevant product types.,RFS,is participated by,[],[],Relation
A product group can simplify rule creation for relevant product types.,Orders,is participated by,[],[],Relation
A product group can simplify rule creation for relevant product types.,SEP Bank Basket areas,is participated by,[],[],Relation
"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",new group,is participated by,[],[],Relation
"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Create Group,is participated by,[],[],Relation
"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",name,is participated by,[],[],Relation
"A new group is added by clicking Create Group, typing the desired name, clicking Create Group again, and clicking Save.",Save,is participated by,[],[],Relation
A default group exists that includes all products.,default group,is participated by,[],[],Relation
A default group exists that includes all products.,products,is participated by,[],[],Relation
This group cannot be removed or renamed.,group,is participated by,[],[],Relation
The product types in the group can be altered.,product types,is participated by,[],[],Relation
The product types in the group can be altered.,group,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",Default Group,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",product types,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",RFS,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",Orders,is participated by,[],[],Relation
"The Default Group contains all product types across RFS, Orders and SEP.",SEP,is participated by,[],[],Relation
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",products,is participated by,[],[],Relation
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",RFS,is participated by,[],[],Relation
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",Orders,is participated by,[],[],Relation
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",SEP,is participated by,[],[],Relation
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",rule,is participated by,[],[],Relation
"Only relevant products for RFS, Orders or SEP apply when a rule uses the default Product Group.",default Product Group,is participated by,[],[],Relation
Each Bank Basket area allows creating Provider Groups and blocking providers.,Bank Basket areas,is participated by,[],[],Relation
Each Bank Basket area allows creating Provider Groups and blocking providers.,RFS,is participated by,[],[],Relation
Each Bank Basket area allows creating Provider Groups and blocking providers.,Orders,is participated by,[],[],Relation
Each Bank Basket area allows creating Provider Groups and blocking providers.,SEP,is participated by,[],[],Relation
Each Bank Basket area allows creating Provider Groups and blocking providers.,Provider Groups,is participated by,[],[],Relation
Each Bank Basket area allows creating Provider Groups and blocking providers.,providers,is participated by,[],[],Relation
"The image displays a green button with ""360T"" text.",image,is participated by,[],[],Relation
"The image displays a green button with ""360T"" text.",green button,is participated by,[],[],Relation
"The image displays a green button with ""360T"" text.",360T,is participated by,[],[],Relation
The button features a white outline and a white background.,button,is participated by,[],[],Relation
The button features a white outline and a white background.,white outline,is participated by,[],[],Relation
The button features a white outline and a white background.,white background,is participated by,[],[],Relation
The text is presented in a stylized font.,text,is participated by,[],[],Relation
The text is presented in a stylized font.,stylized font,is participated by,[],[],Relation
Providers are temporarily blocked from particular request types.,"A Blocked Provider will remain in a Provider Group but will appear with a ""blocked"" symbol.",as a result,[],[],Relation
A Provider should be removed completely.,The relationship should be rejected using the Counterpart Relationship Management tool.,as a result,[],[],Relation
The relationship is rejected using the Counterpart Relationship Management tool.,Please refer to the relevant user guide.,as a result,[],[],Relation
Provider Groups can be edited.,Provider Groups,is participated by,[],[],Relation
Provider Groups can be edited.,values,is participated by,[],[],Relation
Provider Groups can be edited.,rules,is participated by,[],[],Relation
Providers can be temporarily blocked from particular request types.,Providers,is participated by,[],[],Relation
Providers can be temporarily blocked from particular request types.,request types,is participated by,[],[],Relation
Providers can be temporarily blocked from particular request types.,Provider Group,is participated by,[],[],Relation
"A Blocked Provider will remain in a Provider Group and appear with a ""blocked"" symbol.",Blocked Provider,is participated by,[],[],Relation
"A Blocked Provider will remain in a Provider Group and appear with a ""blocked"" symbol.",Provider Group,is participated by,[],[],Relation
"A Blocked Provider will remain in a Provider Group and appear with a ""blocked"" symbol.","""blocked"" symbol",is participated by,[],[],Relation
A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.,Provider,is participated by,[],[],Relation
A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.,relationship,is participated by,[],[],Relation
A relationship should be rejected using the Counterpart Relationship Management tool if a Provider is removed.,Counterpart Relationship Management tool,is participated by,[],[],Relation
Users should refer to the relevant user guide.,user guide,is participated by,[],[],Relation
The Default Group will include all active-relationship providers.,All Bank Baskets will contain one single rule.,at the same time,[],[],Relation
The Default Group will include all active-relationship providers.,New Counterparties will be added to the Default Group.,after,[],[],Relation
All configuration groups are in place.,Individual rules may be defined using the relevant groups for each Bank Basket area.,before,[],[],Relation
Click Add Rule.,Select an option from each of the configuration groups using the drop down menu.,before,[],[],Relation
The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.,Default Group,is participated by,[],[],Relation
The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.,active-relationship providers,is participated by,[],[],Relation
The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.,Bank Basket configuration,is participated by,[],[],Relation
The Default Group will include all active-relationship providers upon initial activation of the Bank Basket configuration.,Counterpart Relationship,is participated by,[],[],Relation
New Counterparties will be added to the Default Group.,New Counterparties,is participated by,[],[],Relation
New Counterparties will be added to the Default Group.,Default Group,is participated by,[],[],Relation
A user clicks Create Group to add a new group.,user,is participated by,[],[],Relation
A user clicks Create Group to add a new group.,Create Group,is participated by,[],[],Relation
A user clicks Create Group to add a new group.,new group,is participated by,[],[],Relation
A user types the desired name.,user,is participated by,[],[],Relation
A user types the desired name.,desired name,is participated by,[],[],Relation
A user clicks Create Group again.,user,is participated by,[],[],Relation
A user clicks Create Group again.,Create Group,is participated by,[],[],Relation
A user clicks Save.,user,is participated by,[],[],Relation
A user clicks Save.,Save,is participated by,[],[],Relation
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",providers,is participated by,[],[],Relation
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",banks,is participated by,[],[],Relation
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",Available Providers,is participated by,[],[],Relation
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",Selected Providers,is participated by,[],[],Relation
"Providers are added or removed by moving banks from ""Available Providers"" to ""Selected Providers"" using arrow buttons.",arrow buttons,is participated by,[],[],Relation
The user selects 'Custom' in a rule parameter.,A criteria can be defined in that moment.,as a result,[],[],Relation
A criteria can be defined in that moment.,A predefined Configuration group is not needed.,as a result,[],[],Relation
No Currency Couple Group was defined for AUD and a specific bank basket should be used for all AUD requests.,The rules shown in the figure below can be defined.,as a result,[],[],Relation
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",user,is participated by,[],[],Relation
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",Configuration Group,is participated by,[],[],Relation
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",previously saved groups,is participated by,[],[],Relation
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",Any,is participated by,[],[],Relation
"The user can explicitly select one of the previously saved groups, ""Any"" or ""Custom"" for each Configuration Group.",Custom,is participated by,[],[],Relation
"The selection of ""Custom"" in each rule parameter allows to define a criteria in that moment.",Custom,is participated by,[],[],Relation
"The selection of ""Custom"" in each rule parameter allows to define a criteria in that moment.",rule parameter,is participated by,[],[],Relation
"The selection of ""Custom"" in each rule parameter allows to define a criteria in that moment.",criteria,is participated by,[],[],Relation
A predefined Configuration group is not needed.,predefined Configuration group,is participated by,[],[],Relation
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,rules,is participated by,[],[],Relation
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,figure below,is participated by,[],[],Relation
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,Currency Couple Group,is participated by,[],[],Relation
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,AUD,is participated by,[],[],Relation
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,bank basket,is participated by,[],[],Relation
Rules shown in the figure below can be defined if no Currency Couple Group was defined for AUD but a specific bank basket should be used for all AUD requests.,AUD requests,is participated by,[],[],Relation
"The image contains the text '360T' in a green, stylized font on a white background.",image,is participated by,[],[],Relation
"The image contains the text '360T' in a green, stylized font on a white background.",text '360T',is participated by,[],[],Relation
"The image contains the text '360T' in a green, stylized font on a white background.","green, stylized font",is participated by,[],[],Relation
"The image contains the text '360T' in a green, stylized font on a white background.",white background,is participated by,[],[],Relation
The text appears to be a logo or brand name.,text,is participated by,[],[],Relation
The text appears to be a logo or brand name.,logo,is participated by,[],[],Relation
The text appears to be a logo or brand name.,brand name,is participated by,[],[],Relation
"A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.",connection,is participated by,[],[],Relation
"A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.","Fri, 06. Jul 2018, 11:28:21 GMT",is participated by,[],[],Relation
"A connection was established on Fri, 06. Jul 2018, 11:28:21 GMT at FFM.",FFM,is participated by,[],[],Relation
RFS requests will use the AUD-Group bank basket based on currency rules.,RFS requests,is participated by,[],[],Relation
RFS requests will use the AUD-Group bank basket based on currency rules.,base currency AUD,is participated by,[],[],Relation
RFS requests will use the AUD-Group bank basket based on currency rules.,Rule 2,is participated by,[],[],Relation
RFS requests will use the AUD-Group bank basket based on currency rules.,terms currency AUD,is participated by,[],[],Relation
RFS requests will use the AUD-Group bank basket based on currency rules.,Rule 3,is participated by,[],[],Relation
RFS requests will use the AUD-Group bank basket based on currency rules.,AUD-Group bank basket,is participated by,[],[],Relation
The tenor can determine the banks for sending a request.,tenor,is participated by,[],[],Relation
The tenor can determine the banks for sending a request.,banks,is participated by,[],[],Relation
The tenor can determine the banks for sending a request.,request,is participated by,[],[],Relation
The POS column indicates rule precedence.,POS column,is participated by,[],[],Relation
The POS column indicates rule precedence.,rules,is participated by,[],[],Relation
The Bank Basket is chosen for each request based on rule order.,individual request,is participated by,[],[],Relation
The Bank Basket is chosen for each request based on rule order.,Bank Basket,is participated by,[],[],Relation
The Bank Basket is chosen for each request based on rule order.,rule order 1,is participated by,[],[],Relation
The Bank Basket is chosen for each request based on rule order.,rule order n,is participated by,[],[],Relation
Rule 1 defines the Bank Basket if a request meets its criteria.,Rule 1,is participated by,[],[],Relation
Rule 1 defines the Bank Basket if a request meets its criteria.,Bank Basket,is participated by,[],[],Relation
Rule 1 defines the Bank Basket if a request meets its criteria.,request,is participated by,[],[],Relation
Rule 1 defines the Bank Basket if a request meets its criteria.,criteria,is participated by,[],[],Relation
Rules should be sorted with the most restrictive definition on top.,rules,is participated by,[],[],Relation
Rules should be sorted with the most restrictive definition on top.,restrictive definition,is participated by,[],[],Relation
A user can select a rule line with a mouse to move it.,existing rule,is participated by,[],[],Relation
A user can select a rule line with a mouse to move it.,list,is participated by,[],[],Relation
A user can select a rule line with a mouse to move it.,mouse,is participated by,[],[],Relation
A user can drag and drop a rule into a desired position.,rule,is participated by,[],[],Relation
A user can drag and drop a rule into a desired position.,desired position,is participated by,[],[],Relation
Configuration of currency Bank Baskets for Supersonic is not yet available.,currency Bank Baskets,is participated by,[],[],Relation
Configuration of currency Bank Baskets for Supersonic is not yet available.,Supersonic,is participated by,[],[],Relation
Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.,Bank Basket selection,is participated by,[],[],Relation
Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.,Supersonic interface,is participated by,[],[],Relation
Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.,SEP Contributors,is participated by,[],[],Relation
Bank Basket selection is configured in the Supersonic interface by defining SEP Contributors for currency pairs.,spot currency pair,is participated by,[],[],Relation
SEP Bank Baskets are used to block SEP providers.,SEP Bank Baskets,is participated by,[],[],Relation
SEP Bank Baskets are used to block SEP providers.,SEP providers,is participated by,[],[],Relation
The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.,Bank Baskets Evaluator Tool,is participated by,[],[],Relation
The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.,user,is participated by,[],[],Relation
The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.,rule,is participated by,[],[],Relation
The Bank Baskets Evaluator Tool assists users in identifying applicable rules for requests.,particular type of request,is participated by,[],[],Relation
The tool can be accessed from the Bridge Administration Homepage.,tool,is participated by,[],[],Relation
The tool can be accessed from the Bridge Administration Homepage.,Bridge Administration Homepage,is participated by,[],[],Relation
The image displays a screenshot of the 360T Bank Baskets Configuration user interface.,image,is participated by,[],[],Relation
The image displays a screenshot of the 360T Bank Baskets Configuration user interface.,screenshot,is participated by,[],[],Relation
The image displays a screenshot of the 360T Bank Baskets Configuration user interface.,360T Bank Baskets Configuration user interface,is participated by,[],[],Relation
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",main area,is participated by,[],[],Relation
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",screen,is participated by,[],[],Relation
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",'Administration Start' page,is participated by,[],[],Relation
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",'Configurations' section,is participated by,[],[],Relation
"The main area of the screen displays the 'Administration Start' page, including 'Configurations' and 'Actions' sections.",'Actions' section,is participated by,[],[],Relation
Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.,'Configurations',is participated by,[],[],Relation
Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.,'Regulatory Data' icon,is participated by,[],[],Relation
Icons for 'Regulatory Data' and 'Bank Baskets' are located under 'Configurations'.,'Bank Baskets' icon,is participated by,[],[],Relation
The tool is accessed using the Bank Basket Evaluator Tool icon.,The user must identify the desired company to evaluate its Bank Basket.,as a result,[],[],Relation
The user uses the icon to access the tool.,The system takes the company Bank Basket based on the current active tab.,as a result,[],[],Relation
The user enters the desired parameters for a request.,The user clicks 'Find Bank Baskets Rule'.,before,[],[],Relation
The user clicks 'Find Bank Baskets Rule'.,The system jumps to and highlights the relevant rule.,as a result,[],[],Relation
Icons are displayed.,'Change Request' icon,is participated by,[],[],Relation
Icons are displayed.,'Wizards' icon,is participated by,[],[],Relation
Icons are displayed.,'Evaluator Tools' icon,is participated by,[],[],Relation
An icon is highlighted.,'Evaluator Tools' icon,is participated by,[],[],Relation
An icon is highlighted.,red dashed border,is participated by,[],[],Relation
The top of the screen contains various interface elements.,top of the screen,is participated by,[],[],Relation
The top of the screen contains various interface elements.,'RFS REQUESTER' tab,is participated by,[],[],Relation
The top of the screen contains various interface elements.,'DEAL TRACKING' tab,is participated by,[],[],Relation
The top of the screen contains various interface elements.,'BRIDGE ADMINISTRATION' tab,is participated by,[],[],Relation
The top of the screen contains various interface elements.,'Preferences' option,is participated by,[],[],Relation
The top of the screen contains various interface elements.,'Administration' option,is participated by,[],[],Relation
The top of the screen contains various interface elements.,'Help' option,is participated by,[],[],Relation
The bottom of the screen shows connection and time details.,bottom of the screen,is participated by,[],[],Relation
The bottom of the screen shows connection and time details.,user's connection,is participated by,[],[],Relation
The bottom of the screen shows connection and time details.,date,is participated by,[],[],Relation
The bottom of the screen shows connection and time details.,time,is participated by,[],[],Relation
The tool can be accessed via an icon.,tool,is participated by,[],[],Relation
The tool can be accessed via an icon.,Bank Basket Evaluator Tool icon,is participated by,[],[],Relation
The tool can be accessed via an icon.,Configuration Data Tabs,is participated by,[],[],Relation
User identification of a company is required for evaluation.,methods,is participated by,[],[],Relation
User identification of a company is required for evaluation.,user,is participated by,[],[],Relation
User identification of a company is required for evaluation.,desired company,is participated by,[],[],Relation
User identification of a company is required for evaluation.,company's Bank Basket,is participated by,[],[],Relation
The system selects the company Bank Basket based on the active tab when the icon is used.,icon,is participated by,[],[],Relation
The system selects the company Bank Basket based on the active tab when the icon is used.,system,is participated by,[],[],Relation
The system selects the company Bank Basket based on the active tab when the icon is used.,company Bank Basket,is participated by,[],[],Relation
The system selects the company Bank Basket based on the active tab when the icon is used.,current active tab,is participated by,[],[],Relation
Desired parameters for a request must be entered.,desired parameters,is participated by,[],[],Relation
Desired parameters for a request must be entered.,particular type of request,is participated by,[],[],Relation
"The ""Find Bank Baskets Rule"" button should be clicked.","""Find Bank Baskets Rule"" button",is participated by,[],[],Relation
Product details need to be entered.,product details,is participated by,[],[],Relation
The system will navigate to and highlight a rule.,system,is participated by,[],[],Relation
The system will navigate to and highlight a rule.,relevant rule,is participated by,[],[],Relation
